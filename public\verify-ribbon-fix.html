<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证飘带修复</title>
    <script src="https://cesium.com/downloads/cesiumjs/releases/1.95/Build/Cesium/Cesium.js"></script>
    <link href="https://cesium.com/downloads/cesiumjs/releases/1.95/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <style>
        html, body, #cesiumContainer {
            width: 100%; height: 100%; margin: 0; padding: 0; overflow: hidden;
        }
        
        .verify-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-width: 350px;
            z-index: 1000;
        }
        
        .verify-panel h3 {
            margin: 0 0 15px 0;
            color: #4CAF50;
            text-align: center;
        }
        
        .test-result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
            border-left: 4px solid;
        }
        
        .test-pass {
            background: rgba(76, 175, 80, 0.2);
            border-color: #4CAF50;
            color: #4CAF50;
        }
        
        .test-fail {
            background: rgba(244, 67, 54, 0.2);
            border-color: #F44336;
            color: #F44336;
        }
        
        .test-info {
            background: rgba(33, 150, 243, 0.2);
            border-color: #2196F3;
            color: #2196F3;
        }
        
        .config-display {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 12px;
        }
        
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }
        
        button:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div id="cesiumContainer"></div>
    
    <div class="verify-panel">
        <h3>🔍 飘带修复验证</h3>
        
        <div id="testResults">
            <div class="test-info">正在运行验证测试...</div>
        </div>
        
        <div>
            <button onclick="runVerification()">🔄 重新验证</button>
            <button onclick="createTestModel()">🧪 创建测试模型</button>
            <button onclick="showConfig()">⚙️ 显示配置</button>
        </div>
        
        <div class="config-display" id="configDisplay" style="display: none;">
            <strong>当前配置:</strong><br>
            <span id="configContent"></span>
        </div>
    </div>

    <script>
        // 初始化 Cesium
        Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.frpOhwF27cIuEfRqTwS8_O0nhiQlCPG9klt0_AvNCZY';
        
        const viewer = new Cesium.Viewer('cesiumContainer', {
            terrainProvider: Cesium.createWorldTerrain(),
            timeline: false,
            animation: false,
            homeButton: false,
            sceneModePicker: false,
            baseLayerPicker: false,
            navigationHelpButton: false,
            fullscreenButton: false,
            vrButton: false
        });

        // 模拟你的实际 EffectManager 和 ModelManager
        class MockEffectManager {
            constructor(viewer, wsUrl) {
                this.viewer = viewer;
                this.wsUrl = wsUrl;
                this.effects = new Map();
                this.ribbonConfig = {
                    maxPoints: 50,
                    wingSpan: 30.0,
                    ribbonWidth: 2.0,
                    color: Cesium.Color.CYAN.withAlpha(0.8),
                    minDistance: 10.0,
                    fadeAlpha: 0.1,
                    rollSensitivity: 1.0
                };
                
                // 模拟 WebSocket 连接状态
                this.ws = { readyState: WebSocket.OPEN };
            }

            createEffect(config, modelId, type) {
                const effectId = `${modelId}_${type}`;
                
                if (type === 7) {
                    this.createRibbonEffect(effectId, config);
                }
                
                return effectId;
            }

            createRibbonEffect(effectId, config) {
                const model = this.viewer.entities.getById(config.modelId);
                if (!model || !model.position) {
                    addTestResult(`❌ 模型 ${config.modelId} 不存在或没有位置`, 'fail');
                    return;
                }

                const effect = {
                    id: effectId,
                    type: 7,
                    config: config,
                    ribbonData: {
                        positions: [],
                        maxPoints: config.customParams?.maxPoints || 50,
                        lastPosition: undefined,
                        wingSpan: config.customParams?.wingSpan || 30.0
                    },
                    isActive: true
                };

                // 创建飘带实体
                const ribbonEntity = this.viewer.entities.add({
                    polyline: {
                        positions: new Cesium.CallbackProperty(() => {
                            return this.updateRibbonPositions(effect);
                        }, false),
                        width: config.customParams?.wingSpan / 5 || 6,
                        material: new Cesium.PolylineGlowMaterialProperty({
                            glowPower: 0.3,
                            color: config.color || Cesium.Color.CYAN.withAlpha(0.8)
                        })
                    }
                });

                effect.entity = ribbonEntity;
                this.effects.set(effectId, effect);
                
                addTestResult(`✅ 飘带效果已创建: ${effectId}`, 'pass');
            }

            updateRibbonPositions(effect) {
                if (!effect.ribbonData) return [];

                const model = this.viewer.entities.getById(effect.config.modelId);
                if (!model || !model.position) return effect.ribbonData.positions;

                const currentTime = Cesium.JulianDate.now();
                const currentPosition = model.position.getValue(currentTime);
                
                if (!currentPosition) return effect.ribbonData.positions;

                // 检查是否需要添加新点
                if (!effect.ribbonData.lastPosition || 
                    Cesium.Cartesian3.distance(currentPosition, effect.ribbonData.lastPosition) > 10.0) {
                    
                    effect.ribbonData.positions.push(currentPosition.clone());
                    effect.ribbonData.lastPosition = currentPosition.clone();

                    // 限制点数
                    if (effect.ribbonData.positions.length > effect.ribbonData.maxPoints) {
                        effect.ribbonData.positions.shift();
                    }
                }

                return effect.ribbonData.positions;
            }

            destroyAllEffects() {
                this.effects.forEach((effect, id) => {
                    if (effect.entity) {
                        this.viewer.entities.remove(effect.entity);
                    }
                });
                this.effects.clear();
            }
        }

        class MockModelManager {
            constructor(viewer, effectManager, config) {
                this.viewer = viewer;
                this.effectManager = effectManager;
                
                // 这里模拟你修复后的配置
                this.config = {
                    maxTrailPoints: 200,
                    trailColor: Cesium.Color.RED,
                    enableRibbonByDefault: true, // 🎯 关键修复
                    trailWidth: 3,
                    modelCacheSize: 2000,
                    lodTransitionMargin: 500,
                    maxTrailPointPoolSize: 5000,
                    trailPointPoolInitialSize: 10,
                    lodDistanceThresholds: {
                        high: 5000,
                        medium: 10000,
                        low: 20000,
                    },
                    ...config
                };
            }

            async createNewModel(item) {
                const position = Cesium.Cartesian3.fromDegrees(item.lo, item.la, item.al);
                
                const entity = this.viewer.entities.add({
                    id: item.na,
                    position: new Cesium.ConstantPositionProperty(position),
                    point: {
                        pixelSize: 15,
                        color: item.si === 'red' ? Cesium.Color.RED : Cesium.Color.BLUE,
                        outlineColor: Cesium.Color.WHITE,
                        outlineWidth: 2
                    },
                    label: {
                        text: item.na,
                        show: true,
                        font: '14px sans-serif',
                        fillColor: Cesium.Color.WHITE,
                        pixelOffset: new Cesium.Cartesian2(0, -30),
                        style: Cesium.LabelStyle.FILL_AND_OUTLINE
                    },
                    orientation: Cesium.Transforms.headingPitchRollQuaternion(
                        position,
                        new Cesium.HeadingPitchRoll(0, 0, 0)
                    )
                });

                // 🎯 关键测试：检查是否自动添加飘带
                if (this.config.enableRibbonByDefault) {
                    this.addRibbonEffect(item.na, item.si, item.type, item.ic);
                    addTestResult(`✅ 自动为模型 ${item.na} 添加飘带 (enableRibbonByDefault: true)`, 'pass');
                } else {
                    addTestResult(`❌ 未自动添加飘带 (enableRibbonByDefault: false)`, 'fail');
                }

                return entity;
            }

            addRibbonEffect(modelId, side, modelType, modelIcon) {
                const ribbonColor = side === 'red' 
                    ? Cesium.Color.RED.withAlpha(0.8)
                    : Cesium.Color.BLUE.withAlpha(0.8);

                const ribbonConfig = {
                    type: 7,
                    modelId: modelId,
                    state: 0,
                    color: ribbonColor,
                    customParams: {
                        maxPoints: this.config.maxTrailPoints,
                        wingSpan: 30.0,
                        minDistance: 10.0,
                        fadeAlpha: 0.1
                    }
                };

                this.effectManager.createEffect(ribbonConfig, modelId, 7);
            }
        }

        // 初始化管理器（模拟你的实际配置）
        const effectManager = new MockEffectManager(viewer, 'ws://localhost:8080/effects');
        const modelManager = new MockModelManager(viewer, effectManager, {
            maxTrailPoints: 200,
            trailColor: Cesium.Color.RED,
            enableRibbonByDefault: true // 🎯 这是修复的关键
        });

        // 测试结果显示
        function addTestResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }

        function clearTestResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        // 验证函数
        async function runVerification() {
            clearTestResults();
            addTestResult('🔍 开始验证飘带修复...', 'info');

            // 1. 检查配置
            addTestResult(`📋 检查配置: enableRibbonByDefault = ${modelManager.config.enableRibbonByDefault}`, 
                modelManager.config.enableRibbonByDefault ? 'pass' : 'fail');

            // 2. 检查 EffectManager
            addTestResult(`📋 EffectManager 初始化: ${effectManager ? '✅' : '❌'}`, 
                effectManager ? 'pass' : 'fail');

            // 3. 检查 WebSocket 状态
            addTestResult(`📡 WebSocket 状态: ${effectManager.ws?.readyState === WebSocket.OPEN ? '连接正常' : '连接异常'}`, 
                effectManager.ws?.readyState === WebSocket.OPEN ? 'pass' : 'fail');

            // 4. 创建测试模型
            try {
                const testModel = {
                    na: 'verify_test_001',
                    ic: 'test_aircraft',
                    lo: 116.0,
                    la: 39.0,
                    al: 8000,
                    si: 'blue',
                    ya: 0,
                    pi: 0,
                    ro: 0,
                    type: 'aircraft'
                };

                await modelManager.createNewModel(testModel);
                addTestResult('✅ 测试模型创建成功', 'pass');

                // 5. 验证飘带效果
                setTimeout(() => {
                    const effect = effectManager.effects.get(`${testModel.na}_7`);
                    if (effect) {
                        addTestResult('✅ 飘带效果自动创建成功', 'pass');
                        addTestResult('🎉 修复验证通过！飘带现在应该能正常显示了', 'pass');
                    } else {
                        addTestResult('❌ 飘带效果未创建', 'fail');
                        addTestResult('⚠️ 可能还有其他问题需要检查', 'fail');
                    }
                }, 500);

            } catch (error) {
                addTestResult(`❌ 测试失败: ${error.message}`, 'fail');
            }
        }

        function createTestModel() {
            const testId = `manual_test_${Date.now()}`;
            const position = Cesium.Cartesian3.fromDegrees(
                116.0 + Math.random() * 0.1, 
                39.0 + Math.random() * 0.1, 
                8000
            );
            
            modelManager.createNewModel({
                na: testId,
                ic: 'manual_test',
                lo: 116.0 + Math.random() * 0.1,
                la: 39.0 + Math.random() * 0.1,
                al: 8000,
                si: Math.random() > 0.5 ? 'red' : 'blue',
                ya: 0,
                pi: 0,
                ro: 0,
                type: 'aircraft'
            });

            // 设置相机视角
            viewer.camera.setView({
                destination: Cesium.Cartesian3.fromDegrees(116.0, 39.0, 15000),
                orientation: {
                    heading: 0.0,
                    pitch: -0.5,
                    roll: 0.0
                }
            });
        }

        function showConfig() {
            const configDiv = document.getElementById('configDisplay');
            const configContent = document.getElementById('configContent');
            
            if (configDiv.style.display === 'none') {
                configContent.innerHTML = `
                    enableRibbonByDefault: ${modelManager.config.enableRibbonByDefault}<br>
                    maxTrailPoints: ${modelManager.config.maxTrailPoints}<br>
                    trailColor: ${modelManager.config.trailColor}<br>
                    WebSocket状态: ${effectManager.ws?.readyState}<br>
                    特效数量: ${effectManager.effects.size}
                `;
                configDiv.style.display = 'block';
            } else {
                configDiv.style.display = 'none';
            }
        }

        // 自动运行验证
        setTimeout(runVerification, 1000);
    </script>
</body>
</html>
