/**
 * 飘带效果诊断工具
 * 用于检查和调试飘带不显示的问题
 */

export class RibbonDiagnostic {
  private viewer: Cesium.Viewer
  private effectManager: any
  private modelManager: any

  constructor(viewer: Cesium.Viewer, effectManager: any, modelManager: any) {
    this.viewer = viewer
    this.effectManager = effectManager
    this.modelManager = modelManager
  }

  /**
   * 运行完整诊断
   */
  public runFullDiagnostic(): void {
    console.log('🔍 开始飘带效果诊断...')
    console.log('=' * 50)

    this.checkBasicSetup()
    this.checkModels()
    this.checkEffects()
    this.checkConfiguration()
    this.checkWebSocket()
    this.provideSolutions()

    console.log('=' * 50)
    console.log('✅ 诊断完成')
  }

  /**
   * 检查基础设置
   */
  private checkBasicSetup(): void {
    console.log('📋 检查基础设置...')

    // 检查 Viewer
    if (!this.viewer) {
      console.error('❌ Cesium Viewer 未初始化')
      return
    }
    console.log('✅ Cesium Viewer 正常')

    // 检查 EffectManager
    if (!this.effectManager) {
      console.error('❌ EffectManager 未初始化')
      return
    }
    console.log('✅ EffectManager 正常')

    // 检查 ModelManager
    if (!this.modelManager) {
      console.error('❌ ModelManager 未初始化')
      return
    }
    console.log('✅ ModelManager 正常')

    // 检查默认飘带配置
    const config = this.modelManager.config || this.modelManager['config']
    if (config && config.enableRibbonByDefault !== undefined) {
      console.log(`✅ 默认飘带配置: ${config.enableRibbonByDefault ? '启用' : '禁用'}`)
      if (!config.enableRibbonByDefault) {
        console.warn('⚠️ 默认飘带功能已禁用，这可能是飘带不显示的原因')
      }
    } else {
      console.warn('⚠️ 无法检查默认飘带配置')
    }
  }

  /**
   * 检查模型
   */
  private checkModels(): void {
    console.log('\n📋 检查模型...')

    const entities = this.viewer.entities.values
    console.log(`📊 总实体数量: ${entities.length}`)

    const modelsWithPosition = entities.filter(entity => 
      entity.model && entity.position
    )
    console.log(`📊 有模型和位置的实体: ${modelsWithPosition.length}`)

    if (modelsWithPosition.length === 0) {
      console.error('❌ 没有找到有效的模型实体')
      console.log('💡 请确保已创建模型并且模型有position属性')
      return
    }

    // 检查前几个模型的详细信息
    modelsWithPosition.slice(0, 3).forEach((entity, index) => {
      console.log(`\n📋 模型 ${index + 1}: ${entity.id}`)
      
      // 检查位置
      try {
        const position = entity.position?.getValue(Cesium.JulianDate.now())
        if (position) {
          const cartographic = Cesium.Cartographic.fromCartesian(position)
          const lon = Cesium.Math.toDegrees(cartographic.longitude)
          const lat = Cesium.Math.toDegrees(cartographic.latitude)
          const alt = cartographic.height
          console.log(`  📍 位置: (${lon.toFixed(3)}, ${lat.toFixed(3)}, ${alt.toFixed(0)}m)`)
        } else {
          console.warn(`  ⚠️ 无法获取位置`)
        }
      } catch (error) {
        console.error(`  ❌ 位置获取错误:`, error)
      }

      // 检查方向
      try {
        const orientation = entity.orientation?.getValue(Cesium.JulianDate.now())
        if (orientation) {
          console.log(`  🧭 方向: 已设置`)
        } else {
          console.warn(`  ⚠️ 未设置方向`)
        }
      } catch (error) {
        console.error(`  ❌ 方向获取错误:`, error)
      }

      // 检查模型属性
      if (entity.model) {
        const modelUri = entity.model.uri?.getValue(Cesium.JulianDate.now())
        console.log(`  🎨 模型URI: ${modelUri || '未设置'}`)
        
        const scale = entity.model.scale?.getValue(Cesium.JulianDate.now())
        console.log(`  📏 缩放: ${scale || '默认'}`)
      }
    })
  }

  /**
   * 检查特效
   */
  private checkEffects(): void {
    console.log('\n📋 检查特效...')

    const effects = this.effectManager.effects
    if (!effects) {
      console.error('❌ 无法访问特效集合')
      return
    }

    console.log(`📊 总特效数量: ${effects.size}`)

    // 检查飘带特效（类型7）
    const ribbonEffects = Array.from(effects.values()).filter(effect => effect.type === 7)
    console.log(`📊 飘带特效数量: ${ribbonEffects.length}`)

    if (ribbonEffects.length === 0) {
      console.error('❌ 没有找到飘带特效')
      console.log('💡 这可能是飘带不显示的主要原因')
      return
    }

    // 检查前几个飘带特效的详细信息
    ribbonEffects.slice(0, 3).forEach((effect, index) => {
      console.log(`\n📋 飘带特效 ${index + 1}: ${effect.id}`)
      console.log(`  🎯 模型ID: ${effect.config.modelId}`)
      console.log(`  🎨 颜色: ${effect.config.color}`)
      console.log(`  ⚙️ 状态: ${effect.config.state === 0 ? '创建' : '销毁'}`)
      
      // 检查实体是否存在
      if (effect.entity) {
        console.log(`  ✅ 飘带实体已创建`)
        
        // 检查polyline属性
        if (effect.entity.polyline) {
          console.log(`  ✅ Polyline属性存在`)
          
          try {
            const positions = effect.entity.polyline.positions?.getValue(Cesium.JulianDate.now())
            console.log(`  📊 飘带点数: ${positions ? positions.length : 0}`)
            
            const width = effect.entity.polyline.width?.getValue(Cesium.JulianDate.now())
            console.log(`  📏 飘带宽度: ${width || '未设置'}`)
          } catch (error) {
            console.error(`  ❌ 获取飘带属性错误:`, error)
          }
        } else {
          console.error(`  ❌ Polyline属性不存在`)
        }
      } else {
        console.error(`  ❌ 飘带实体未创建`)
      }

      // 检查飘带数据
      if (effect.ribbonData) {
        console.log(`  📊 飘带数据点数: ${effect.ribbonData.positions.length}`)
        console.log(`  📏 翼展: ${effect.ribbonData.wingSpan}m`)
      } else {
        console.warn(`  ⚠️ 飘带数据未初始化`)
      }
    })
  }

  /**
   * 检查配置
   */
  private checkConfiguration(): void {
    console.log('\n📋 检查配置...')

    // 检查飘带配置
    const ribbonConfig = this.effectManager.ribbonConfig
    if (ribbonConfig) {
      console.log('✅ 飘带配置存在:')
      console.log(`  📊 最大点数: ${ribbonConfig.maxPoints}`)
      console.log(`  📏 翼展: ${ribbonConfig.wingSpan}m`)
      console.log(`  📏 基础宽度: ${ribbonConfig.ribbonWidth}px`)
      console.log(`  📏 最小距离: ${ribbonConfig.minDistance}m`)
      console.log(`  🎨 颜色: ${ribbonConfig.color}`)
    } else {
      console.warn('⚠️ 飘带配置不存在')
    }

    // 检查模型管理器配置
    const modelConfig = this.modelManager.config || this.modelManager['config']
    if (modelConfig) {
      console.log('✅ 模型管理器配置存在:')
      console.log(`  📊 最大尾迹点数: ${modelConfig.maxTrailPoints}`)
      console.log(`  📏 尾迹宽度: ${modelConfig.trailWidth}`)
      console.log(`  🎯 默认飘带: ${modelConfig.enableRibbonByDefault}`)
    } else {
      console.warn('⚠️ 模型管理器配置不存在')
    }
  }

  /**
   * 检查WebSocket连接
   */
  private checkWebSocket(): void {
    console.log('\n📋 检查WebSocket连接...')

    const ws = this.effectManager.ws
    if (ws) {
      console.log(`📡 WebSocket状态: ${ws.readyState}`)
      switch (ws.readyState) {
        case WebSocket.CONNECTING:
          console.log('🔄 正在连接...')
          break
        case WebSocket.OPEN:
          console.log('✅ 连接正常')
          break
        case WebSocket.CLOSING:
          console.warn('⚠️ 正在关闭连接')
          break
        case WebSocket.CLOSED:
          console.warn('⚠️ 连接已关闭')
          break
      }
    } else {
      console.warn('⚠️ WebSocket未初始化')
    }
  }

  /**
   * 提供解决方案
   */
  private provideSolutions(): void {
    console.log('\n💡 常见问题解决方案:')

    console.log('\n1. 飘带完全不显示:')
    console.log('   - 检查 enableRibbonByDefault 是否为 true')
    console.log('   - 确保模型有 position 和 orientation 属性')
    console.log('   - 检查 EffectManager 是否正确初始化')

    console.log('\n2. 飘带创建但不更新:')
    console.log('   - 确保模型在移动（位置变化）')
    console.log('   - 检查 minDistance 设置是否过大')
    console.log('   - 验证模型的 orientation 是否正确设置')

    console.log('\n3. 飘带宽度异常:')
    console.log('   - 检查翼展数据是否正确')
    console.log('   - 验证模型缩放设置')
    console.log('   - 检查相机距离和视角')

    console.log('\n4. 性能问题:')
    console.log('   - 减少 maxTrailPoints 数量')
    console.log('   - 增加 minDistance 阈值')
    console.log('   - 限制同时显示的飘带数量')
  }

  /**
   * 快速修复尝试
   */
  public quickFix(): void {
    console.log('🔧 尝试快速修复...')

    // 1. 启用默认飘带
    if (this.modelManager.setRibbonByDefault) {
      this.modelManager.setRibbonByDefault(true)
      console.log('✅ 已启用默认飘带')
    }

    // 2. 为所有现有模型添加飘带
    if (this.modelManager.addRibbonToAllModels) {
      this.modelManager.addRibbonToAllModels()
      console.log('✅ 已为所有模型添加飘带')
    }

    // 3. 等待一段时间后检查结果
    setTimeout(() => {
      const effects = this.effectManager.effects
      const ribbonCount = Array.from(effects.values()).filter(effect => effect.type === 7).length
      console.log(`📊 修复后飘带数量: ${ribbonCount}`)
    }, 1000)
  }

  /**
   * 创建测试模型
   */
  public createTestModel(): void {
    console.log('🧪 创建测试模型...')

    const testModelId = 'diagnostic_test_model'
    
    // 移除可能存在的测试模型
    const existingEntity = this.viewer.entities.getById(testModelId)
    if (existingEntity) {
      this.viewer.entities.remove(existingEntity)
    }

    // 创建简单的测试模型
    const entity = this.viewer.entities.add({
      id: testModelId,
      name: '诊断测试模型',
      position: Cesium.Cartesian3.fromDegrees(116.0, 39.0, 8000),
      orientation: Cesium.Transforms.headingPitchRollQuaternion(
        Cesium.Cartesian3.fromDegrees(116.0, 39.0, 8000),
        new Cesium.HeadingPitchRoll(0, 0, 0)
      ),
      point: {
        pixelSize: 10,
        color: Cesium.Color.YELLOW,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2
      },
      label: {
        text: '测试模型',
        font: '14pt sans-serif',
        pixelOffset: new Cesium.Cartesian2(0, -40),
        fillColor: Cesium.Color.WHITE,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE
      }
    })

    console.log('✅ 测试模型已创建')

    // 尝试为测试模型添加飘带
    if (this.modelManager.addRibbonEffect) {
      try {
        this.modelManager.addRibbonEffect(testModelId, 'blue', 'aircraft', 'test_aircraft')
        console.log('✅ 已为测试模型添加飘带')
      } catch (error) {
        console.error('❌ 为测试模型添加飘带失败:', error)
      }
    }

    // 设置相机视角到测试模型
    this.viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(116.0, 39.0, 15000),
      orientation: {
        heading: 0.0,
        pitch: -0.5,
        roll: 0.0
      }
    })

    console.log('📷 相机已定位到测试模型')
  }
}

// 导出便捷函数
export function diagnoseRibbonIssues(viewer: Cesium.Viewer, effectManager: any, modelManager: any): void {
  const diagnostic = new RibbonDiagnostic(viewer, effectManager, modelManager)
  diagnostic.runFullDiagnostic()
}

export function quickFixRibbons(viewer: Cesium.Viewer, effectManager: any, modelManager: any): void {
  const diagnostic = new RibbonDiagnostic(viewer, effectManager, modelManager)
  diagnostic.quickFix()
}

export function createTestRibbonModel(viewer: Cesium.Viewer, effectManager: any, modelManager: any): void {
  const diagnostic = new RibbonDiagnostic(viewer, effectManager, modelManager)
  diagnostic.createTestModel()
}
