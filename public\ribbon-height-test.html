<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飘带高度测试</title>
    <script src="https://cesium.com/downloads/cesiumjs/releases/1.95/Build/Cesium/Cesium.js"></script>
    <link href="https://cesium.com/downloads/cesiumjs/releases/1.95/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <style>
        html, body, #cesiumContainer {
            width: 100%; height: 100%; margin: 0; padding: 0; overflow: hidden;
        }
        
        .test-panel {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: Arial, sans-serif;
            z-index: 1000;
            max-width: 400px;
        }
        
        .test-panel h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }
        
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }
        
        button:hover {
            background: #1976D2;
        }
        
        .info {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
            color: #2196F3;
        }
        
        .height-info {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div id="cesiumContainer"></div>
    
    <div class="test-panel">
        <h3>📏 飘带高度测试</h3>
        
        <div>
            <button onclick="createTestFlight()">🛩️ 创建测试飞行</button>
            <button onclick="showHeightInfo()">📊 显示高度信息</button>
            <button onclick="clearAll()">🗑️ 清除所有</button>
        </div>
        
        <div id="heightInfo" class="info">
            点击"创建测试飞行"开始测试
        </div>
        
        <div id="realTimeInfo" class="info height-info" style="display: none;">
            实时高度信息将在这里显示
        </div>
    </div>

    <script>
        // 初始化 Cesium
        Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJhM2QyZGU5Ny1hM2RjLTQ0NjYtOWI5Ny1jY2RmNzIzMzE2ODAiLCJpZCI6MTM0ODg3LCJpYXQiOjE3NTEwMzM5Mjl9.frpOhwF27cIuEfRqTwS8_O0nhiQlCPG9klt0_AvNCZY';
        
        const viewer = new Cesium.Viewer('cesiumContainer', {
            timeline: true,
            animation: true,
            homeButton: false,
            sceneModePicker: false,
            baseLayerPicker: false,
            navigationHelpButton: false,
            fullscreenButton: false,
            vrButton: false
        });

        let aircraft = null;
        let ribbonEntity = null;
        let heightMonitor = null;

        // 高度测试飘带管理器
        class HeightTestRibbonManager {
            constructor(viewer) {
                this.viewer = viewer;
                this.ribbonData = {
                    positions: [],
                    maxPoints: 50,
                    lastPosition: null
                };
            }

            addRibbon(aircraftId) {
                const aircraft = this.viewer.entities.getById(aircraftId);
                if (!aircraft) return;

                // 创建飘带实体
                ribbonEntity = this.viewer.entities.add({
                    id: `${aircraftId}_ribbon`,
                    polyline: {
                        positions: new Cesium.CallbackProperty(() => {
                            return this.updateRibbonPositions(aircraftId);
                        }, false),
                        width: 37.0,
                        material: new Cesium.PolylineGlowMaterialProperty({
                            glowPower: 0.3,
                            color: Cesium.Color.CYAN.withAlpha(0.8)
                        }),
                        clampToGround: false
                    }
                });

                updateInfo('✅ 飘带已添加，开始高度监控');
            }

            updateRibbonPositions(aircraftId) {
                const aircraft = this.viewer.entities.getById(aircraftId);
                if (!aircraft || !aircraft.position) {
                    return this.ribbonData.positions;
                }

                const currentTime = this.viewer.clock.currentTime;
                const currentPosition = aircraft.position.getValue(currentTime);
                
                if (!currentPosition) {
                    return this.ribbonData.positions;
                }

                // 检查是否需要添加新点
                if (!this.ribbonData.lastPosition || 
                    Cesium.Cartesian3.distance(currentPosition, this.ribbonData.lastPosition) > 100.0) {
                    
                    // 直接使用飞机位置，不做任何高度偏移
                    this.ribbonData.positions.push(currentPosition.clone());
                    this.ribbonData.lastPosition = currentPosition.clone();

                    // 限制点数
                    if (this.ribbonData.positions.length > this.ribbonData.maxPoints) {
                        this.ribbonData.positions.shift();
                    }

                    // 更新高度信息
                    this.updateHeightDisplay(currentPosition);
                }

                return this.ribbonData.positions;
            }

            updateHeightDisplay(position) {
                const cartographic = Cesium.Cartographic.fromCartesian(position);
                const height = cartographic.height;
                const lon = Cesium.Math.toDegrees(cartographic.longitude);
                const lat = Cesium.Math.toDegrees(cartographic.latitude);

                const realTimeInfo = document.getElementById('realTimeInfo');
                realTimeInfo.style.display = 'block';
                realTimeInfo.innerHTML = `
                    <strong>实时位置信息：</strong><br>
                    经度: ${lon.toFixed(6)}°<br>
                    纬度: ${lat.toFixed(6)}°<br>
                    高度: ${height.toFixed(2)} 米<br>
                    飘带点数: ${this.ribbonData.positions.length}
                `;
            }

            clear() {
                this.ribbonData.positions = [];
                this.ribbonData.lastPosition = null;
                if (ribbonEntity) {
                    this.viewer.entities.remove(ribbonEntity);
                    ribbonEntity = null;
                }
            }
        }

        // 初始化飘带管理器
        const ribbonManager = new HeightTestRibbonManager(viewer);

        // 更新信息显示
        function updateInfo(message) {
            const heightInfo = document.getElementById('heightInfo');
            heightInfo.textContent = message;
            console.log(message);
        }

        // 创建测试飞行
        function createTestFlight() {
            // 清除之前的测试
            clearAll();

            const aircraftId = 'height_test_aircraft';
            
            // 创建飞行路径 - 不同高度的航点
            const startTime = Cesium.JulianDate.now();
            const stopTime = Cesium.JulianDate.addSeconds(startTime, 120, new Cesium.JulianDate());
            
            const property = new Cesium.SampledPositionProperty();
            
            // 创建高度变化的飞行路径
            const waypoints = [
                { lon: 116.0, lat: 39.0, alt: 5000, time: 0 },   // 起始高度 5km
                { lon: 116.1, lat: 39.0, alt: 8000, time: 30 },  // 爬升到 8km
                { lon: 116.2, lat: 39.0, alt: 6000, time: 60 },  // 下降到 6km
                { lon: 116.3, lat: 39.0, alt: 10000, time: 90 }, // 爬升到 10km
                { lon: 116.4, lat: 39.0, alt: 7000, time: 120 }  // 下降到 7km
            ];
            
            waypoints.forEach(point => {
                const time = Cesium.JulianDate.addSeconds(startTime, point.time, new Cesium.JulianDate());
                const position = Cesium.Cartesian3.fromDegrees(point.lon, point.lat, point.alt);
                property.addSample(time, position);
            });
            
            property.setInterpolationOptions({
                interpolationDegree: 2,
                interpolationAlgorithm: Cesium.HermitePolynomialApproximation
            });
            
            // 创建飞机实体
            aircraft = viewer.entities.add({
                id: aircraftId,
                position: property,
                orientation: new Cesium.VelocityOrientationProperty(property),
                point: {
                    pixelSize: 15,
                    color: Cesium.Color.YELLOW,
                    outlineColor: Cesium.Color.BLACK,
                    outlineWidth: 2
                },
                label: {
                    text: '高度测试飞机',
                    font: '12px sans-serif',
                    fillColor: Cesium.Color.WHITE,
                    pixelOffset: new Cesium.Cartesian2(0, -30)
                }
            });
            
            // 设置时钟
            viewer.clock.startTime = startTime;
            viewer.clock.stopTime = stopTime;
            viewer.clock.currentTime = startTime;
            viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP;
            viewer.clock.multiplier = 2;
            viewer.clock.shouldAnimate = true;
            
            // 添加飘带
            ribbonManager.addRibbon(aircraftId);
            
            // 设置相机视角
            viewer.camera.setView({
                destination: Cesium.Cartesian3.fromDegrees(116.2, 39.0, 20000),
                orientation: {
                    heading: 0.0,
                    pitch: -0.3,
                    roll: 0.0
                }
            });
            
            updateInfo('✈️ 高度测试飞行已创建，观察飘带是否跟随飞机高度变化');
        }

        // 显示高度信息
        function showHeightInfo() {
            if (!aircraft) {
                updateInfo('❌ 请先创建测试飞行');
                return;
            }

            const currentTime = viewer.clock.currentTime;
            const position = aircraft.position.getValue(currentTime);
            
            if (position) {
                const cartographic = Cesium.Cartographic.fromCartesian(position);
                const height = cartographic.height;
                const lon = Cesium.Math.toDegrees(cartographic.longitude);
                const lat = Cesium.Math.toDegrees(cartographic.latitude);

                updateInfo(`📊 当前飞机位置: 经度 ${lon.toFixed(6)}°, 纬度 ${lat.toFixed(6)}°, 高度 ${height.toFixed(2)} 米`);
                
                // 检查飘带点的高度
                if (ribbonManager.ribbonData.positions.length > 0) {
                    const lastRibbonPoint = ribbonManager.ribbonData.positions[ribbonManager.ribbonData.positions.length - 1];
                    const ribbonCartographic = Cesium.Cartographic.fromCartesian(lastRibbonPoint);
                    const ribbonHeight = ribbonCartographic.height;
                    
                    console.log(`飞机高度: ${height.toFixed(2)}m, 飘带高度: ${ribbonHeight.toFixed(2)}m, 差值: ${(height - ribbonHeight).toFixed(2)}m`);
                }
            }
        }

        // 清除所有
        function clearAll() {
            if (aircraft) {
                viewer.entities.remove(aircraft);
                aircraft = null;
            }
            
            ribbonManager.clear();
            viewer.clock.shouldAnimate = false;
            
            const realTimeInfo = document.getElementById('realTimeInfo');
            realTimeInfo.style.display = 'none';
            
            updateInfo('🗑️ 已清除所有测试内容');
        }

        // 初始化提示
        updateInfo('📏 飘带高度测试工具已准备就绪');
    </script>
</body>
</html>
