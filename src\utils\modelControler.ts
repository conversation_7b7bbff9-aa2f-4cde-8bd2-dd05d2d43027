/*
 * @Author: 老范
 * @Date: 2025-04-01 15:25:08
 * @LastEditors: 老范
 * @LastEditTime: 2025-04-28 19:12:02
 * @Description: 请填写简介
 */
// import * as Cesium from 'cesium'
import { getModels } from '../api/cjgl/situation'
import { TrailManager } from './primitive'
// 类型定义

type ModelUpdateData = {
  na: string
  ic: string
  lo: number
  la: number
  al: number
  si: string
  ya?: number // yaw
  pi?: number //pitch
  ro?: number //roll
  type: string
}

type ModelConfig = {
  maxTrailPoints: number // 尾迹最大点数
  trailWidth: number // 尾迹线宽
  trailColor: Cesium.Color
  modelCacheSize: number // 模型池缓存数量
  lodTransitionMargin: number // 防止LOD频繁切换的缓冲距离
  maxTrailPointPoolSize: number // 对象池最大容量
  trailPointPoolInitialSize: number // 初始池大小
  enableRibbonByDefault: boolean // 是否默认为所有模型添加飘带效果
  lodDistanceThresholds: {
    // LOD距离阈值
    high: number
    medium: number
    low: number
  }
}

export class ModelManager {
  private viewer: Cesium.Viewer
  private config: ModelConfig
  // 模型池

  private modelPool = new Map<string, Cesium.Model>()

  private positionPool = new Map<string, Cesium.Cartesian3>()

  // 待回收模型队列

  private recycleQueue: string[] = []
  // trailSystem: TrailSystem;
  trailPrimitive: any
  effectManager: any

  constructor(
    viewer: Cesium.Viewer,
    effectManager: any,
    config?: Partial<ModelConfig>
  ) {
    this.viewer = viewer
    this.effectManager = effectManager

    this.config = {
      maxTrailPoints: 10,
      trailWidth: 3,
      trailColor: Cesium.Color.CYAN.withAlpha(0.7),
      modelCacheSize: 2000,
      lodTransitionMargin: 500, // 单位：米
      maxTrailPointPoolSize: 5000, // 最多缓存5000个点对象
      trailPointPoolInitialSize: 10, // 初始化时预创建1000个
      enableRibbonByDefault: true, // 默认为所有模型添加飘带效果
      lodDistanceThresholds: {
        high: 5000, // 5km内高精度模型
        medium: 10000, // 10km中精度
        low: 20000, // 20km以上低精度
      },
      ...config,
    }
    this.trailPrimitive = new TrailManager(viewer)
  }

  // 批量更新模型 (主入口)

  public async batchUpdate(data: ModelUpdateData[]) {
    const cameraPosition = this.viewer.camera.positionWC
    // let atime = Date.now();

    data.forEach(item => {
      // 1. 位置转换
      const position = Cesium.Cartesian3.fromDegrees(
        item.lo,
        item.la,
        item.al | 0
      )
      // 2. 模型存在性检查
      if (this.modelPool.has(item.na)) {
        this.updateExistingModel(item.na, position, item)
        this.trailPrimitive.updateTrail(item.na, [
          { lo: item.lo, la: item.la, al: item.al | 0 },
        ])
      } else {
        // this.createNewModel(item, position)
        // this.trailPrimitive.addTrail(item.na, [
        //   { lo: item.lo, la: item.la, al: item.al | 0 },
        //   { lo: item.lo, la: item.la, al: item.al | 0 },
        // ])
      }
      // 3. 更新尾迹
      // 4. 更新位置池
      // this.positionPool.set(item.na, position);
      // 5. LOD控制
      // this.applyLod(item.na, position, cameraPosition);
    })
    // this.trailPrimitive.batchUpdate(data);
    // 6. 清理不可见模型
    // this.cleanupInvisibleModels(cameraPosition);
  }

  // 创建新模型

  public async createNewModel(item: ModelUpdateData) {
    const modelColor =
      item.si === 'red'
        ? Cesium.Color.RED.withAlpha(1.0)
        : Cesium.Color.BLUE.withAlpha(1.0)
    try {
      const position = Cesium.Cartesian3.fromDegrees(
        item.lo,
        item.la,
        item.al | 0
      )
      // const model = getModels({ name: item.ic })
      const model = await this.loadModelWithLod(item.ic, position)
      console.log('创建')

      // 预加载所有LOD级别
      //   this.preloadLodModels(item.type);
      const entity = this.viewer.entities.add({
        id: item.na,
        position: new Cesium.ConstantPositionProperty(position),
        // model: {
        //   uri: '/assets/models/f18.glb',
        //   minimumPixelSize: 128,
        //   maximumScale: 512,
        // },
        model: {
          uri: model.uri,
          minimumPixelSize: 128,
          maximumScale: 512,
          color: modelColor,
        },

        orientation: this.calculateOrientation(
          position,
          item.ya,
          item.pi,
          item.ro
        ),
        label: {
          text: item.na,
          show: true,
          font: '16px sans-serif',
          showBackground: false,
          fillColor: modelColor,
          pixelOffset: new Cesium.Cartesian3(0, -30, 0),
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 1.5,
        },
      })
      // 加入对象池
      this.modelPool.set(item.na, entity.model)

      // 为所有模型添加飘带效果（如果启用）
      if (this.config.enableRibbonByDefault) {
        this.addRibbonEffect(item.na, item.si, item.type, item.ic)
      }

      // 管理回收队列
      if (this.recycleQueue.length >= this.config.modelCacheSize) {
        const oldId = this.recycleQueue.shift()!

        this.destroyModel(oldId)
      }

      this.recycleQueue.push(item.na)
    } catch (error) {
      console.error(`Failed to load model ${item.type}:`, error)
    }
  }
  private preloadLodModels(type: string) {
    ;['high', 'medium', 'low'].forEach(lod => {
      const uri = `models/${type}_${lod}.glb`
      if (!this.modelCache.has(uri)) {
        this.loadModelWithCache(uri)
      }
    })
  }
  // 在 ModelManager 类中添加以下方法
  private applyLod(
    id: string,
    modelPosition: Cesium.Cartesian3,
    cameraPosition: Cesium.Cartesian3
  ) {
    const modelEntity = this.viewer.entities.getById(id)
    if (!modelEntity || !modelEntity.model) return

    // 计算与相机的距离
    const distance = Cesium.Cartesian3.distance(modelPosition, cameraPosition)

    // 获取当前模型配置
    const currentUri = modelEntity.model.uri?.getValue(Cesium.JulianDate.now())
    const modelType = this.getModelTypeFromUri(currentUri) // 从URI解析类型
    const currentLod = this.getCurrentLodLevel(currentUri)
    const distanceToHigh = this.config.lodDistanceThresholds.high
    const distanceToMedium = this.config.lodDistanceThresholds.medium
    const margin = this.config.lodTransitionMargin

    let targetLod = 'medium'
    switch (currentLod) {
      case 'high':
        targetLod = distance > distanceToHigh + margin ? 'medium' : 'high'
        break

      case 'medium':
        if (distance < distanceToHigh - margin) {
          targetLod = 'high'
        } else if (distance > distanceToMedium + margin) {
          targetLod = 'low'
        } else {
          targetLod = 'medium'
        }
        break

      case 'low':
        targetLod = distance < distanceToMedium - margin ? 'medium' : 'low'
        break
    }

    // 如果LOD需要更新
    // if (!currentUri?.includes(targetLod)) {
    //   const newUri = `models/${modelType}_${targetLod}.glb`;

    //   // 异步加载新模型（带缓存检查）
    //   this.loadModelWithCache(newUri)
    //     .then(() => {
    //       modelEntity.model!.uri = newUri;

    //       // 保持模型方向一致性
    //       if (modelEntity.orientation) {
    //         modelEntity.orientation = modelEntity.orientation.clone();
    //       }
    //     })
    //     .catch(err => {
    //       console.warn(`LOD update failed for ${id}:`, err);
    //     });
    // }
  }
  private getCurrentLodLevel(uri?: string): 'high' | 'medium' | 'low' {
    if (!uri) return 'medium' // 默认值

    // 匹配URI中的LOD层级（假设URI格式：models/[type]_[lod].glb）
    const lodMatch = uri.match(/_([a-z]+)\.glb$/i)

    if (lodMatch && lodMatch[1]) {
      const lod = lodMatch[1].toLowerCase() as any
      return ['high', 'medium', 'low'].includes(lod) ? lod : 'medium' // 无效值回退
    }

    return 'medium' // 默认值
  }
  // 添加辅助方法
  private modelCache = new Map<string, Promise<void>>()

  private async loadModelWithCache(uri: string): Promise<void> {
    // 已加载的模型直接返回
    if (this.modelCache.has(uri)) {
      return this.modelCache.get(uri)!
    }

    const promise = new Promise<void>((resolve, reject) => {
      // 使用Cesium内置资源加载器
      Cesium.Resource.fetchArrayBuffer(uri)
        .then(() => resolve())
        .catch(reject)
    })

    this.modelCache.set(uri, promise)
    return promise
  }

  private getModelTypeFromUri(uri?: string): string {
    if (!uri) return 'default'
    const matches = uri.match(/models\/(.*?)_/)
    return matches ? matches[1] : 'default'
  }
  // 加载带LOD的模型

  private async loadModelWithLod(type: string, position: Cesium.Cartesian3) {
    const distance = this.calculateDistanceToCamera(position)

    let uri: string

    // if (distance < this.config.lodDistanceThresholds.high) {
    //   uri = `models/${type}_high.glb`;
    // } else if (distance < this.config.lodDistanceThresholds.medium) {
    //   uri = `models/${type}_medium.glb`;
    // } else {
    //   uri = `models/${type}_low.glb`;
    // }

    // 这里可以添加模型预加载逻辑
    return {
      uri: `${
        (window as any).GVJ.URLS.pluginServer
      }api/v1/model3d/glb/view?name=${type}`,
    }

    // return { uri: `/assets/models/${type}.glb` }
  }

  // 更新现有模型

  private updateExistingModel(
    id: string,
    newPosition: Cesium.Cartesian3,
    item: {
      na?: string
      lo?: number
      la?: number
      al?: number
      ya: any
      pi: any
      ro: any
      type?: string
    }
  ) {
    const entity = this.viewer.entities.getById(id)
    if (entity) {
      entity.position = newPosition // 让Cesium内部处理矩阵计算
      this.positionPool.set(id, newPosition.clone())
      if (item.ya) {
        const orientation = this.calculateOrientation(
          newPosition,
          item.ya ?? 0,
          item.pi ?? 0,
          item.ro ?? 0
        )
        entity.orientation = new Cesium.ConstantProperty(orientation)
      }
    }
  }

  // 计算模型方向
  private calculateOrientation(
    position: Cesium.Cartesian3,
    yaw: number | undefined,
    pitch: number | undefined,
    roll: number | undefined
  ) {
    return Cesium.Transforms.headingPitchRollQuaternion(
      position,
      Cesium.HeadingPitchRoll.fromDegrees(yaw, pitch, roll),
      Cesium.Ellipsoid.WGS84,
      Cesium.Transforms.northWestUpToFixedFrame
    )
  }
  // 清理不可见模型

  private cleanupInvisibleModels(cameraPosition: Cesium.Cartesian3) {
    this.positionPool.forEach((position, id) => {
      if (!this.isPositionVisible(position, cameraPosition)) {
        this.destroyModel(id)
      }
    })
  }
  // 判断位置是否在视锥内
  private isPositionVisible(
    position: Cesium.Cartesian3,
    cameraPosition: Cesium.Cartesian3
  ): boolean {
    const distance = Cesium.Cartesian3.distance(position, cameraPosition)
    return distance < this.config.lodDistanceThresholds.low * 2
  }
  // 销毁模型

  public destroyModel(id: string) {
    const model = this.modelPool.get(id)
    const trail = this.trailPrimitive.trails.get(id)
    if (model) {
      this.viewer.entities.removeById(id)
      this.modelPool.delete(id)
      this.positionPool.delete(id)
    }
    if (trail) {
      this.trailPrimitive.removeTrail(id)
    }
    this.effectManager.destroyEffectByModelId(id)
    // this.effectManager.destroy()
  }

  // 计算到相机的距离

  private calculateDistanceToCamera(position: Cesium.Cartesian3): number {
    return Cesium.Cartesian3.distance(position, this.viewer.camera.positionWC)
  }

  // 重置所有模型
  public reset() {
    this.modelPool.forEach((_, id) => {
      this.destroyModel(id)
      this.trailPrimitive.removeTrail(id)
    })
    this.effectManager.destroy()
  }

  /**
   * 为模型添加飘带效果
   */
  private addRibbonEffect(modelId: string, side: string, modelType?: string, modelIcon?: string): void {
    try {
      const ribbonColor =
        side === 'red'
          ? Cesium.Color.RED.withAlpha(0.8)
          : Cesium.Color.BLUE.withAlpha(0.8)

      // 根据模型类型确定飘带参数
      const ribbonParams = this.getRibbonParams(modelId, modelType, modelIcon)

      const ribbonConfig = {
        type: 7 as const, // 飘带效果类型
        modelId: modelId,
        state: 0 as const, // 创建状态
        color: ribbonColor,
        customParams: {
          maxPoints: this.config.maxTrailPoints,
          wingSpan: ribbonParams.wingSpan,
          minDistance: ribbonParams.minDistance,
          fadeAlpha: 0.1,
          rollSensitivity: ribbonParams.rollSensitivity,
        },
      }

      this.effectManager.createEffect(ribbonConfig, modelId, 7)
      console.log(
        `Added ribbon effect for model: ${modelId}, type: ${modelType || 'unknown'}, wingSpan: ${ribbonParams.wingSpan}m`
      )
    } catch (error) {
      console.error(`Failed to add ribbon effect for ${modelId}:`, error)
    }
  }

  /**
   * 移除模型飘带效果
   */
  public removeRibbonEffect(modelId: string): void {
    this.effectManager.destroyEffectByModelId(modelId)
  }

  /**
   * 根据模型类型获取飘带参数
   */
  private getRibbonParams(modelId: string, modelType?: string, modelIcon?: string): {
    wingSpan: number
    minDistance: number
    rollSensitivity: number
  } {
    const modelIdLower = modelId.toLowerCase()
    const modelTypeLower = (modelType || '').toLowerCase()
    const modelIconLower = (modelIcon || '').toLowerCase()

    // 判断是否为飞机类型
    const isAircraft =
      modelTypeLower === 'aircraft' ||
      modelIconLower.includes('aircraft') ||
      modelIconLower.includes('plane') ||
      modelIconLower.includes('fighter') ||
      modelIconLower.includes('bomber') ||
      this.isKnownAircraftType(modelIdLower) ||
      this.isKnownAircraftType(modelIconLower)

    if (isAircraft) {
      // 飞机类型：使用实际翼展数据
      return {
        wingSpan: this.getAircraftWingSpan(modelId),
        minDistance: 15.0,
        rollSensitivity: 1.0
      }
    }

    // 判断是否为舰船类型
    const isShip =
      modelTypeLower === 'ship' ||
      modelTypeLower === 'vessel' ||
      modelIconLower.includes('ship') ||
      modelIconLower.includes('boat') ||
      modelIconLower.includes('carrier') ||
      modelIconLower.includes('destroyer') ||
      modelIconLower.includes('frigate') ||
      modelIconLower.includes('submarine')

    if (isShip) {
      // 舰船类型：较大的飘带参数
      return {
        wingSpan: 50.0, // 舰船宽度
        minDistance: 25.0,
        rollSensitivity: 0.3
      }
    }

    // 判断是否为地面车辆
    const isVehicle =
      modelTypeLower === 'vehicle' ||
      modelTypeLower === 'tank' ||
      modelIconLower.includes('tank') ||
      modelIconLower.includes('vehicle') ||
      modelIconLower.includes('truck') ||
      modelIconLower.includes('car')

    if (isVehicle) {
      // 地面车辆：较小的飘带参数
      return {
        wingSpan: 8.0,
        minDistance: 5.0,
        rollSensitivity: 0.5
      }
    }

    // 默认参数（适用于未知类型的模型）
    return {
      wingSpan: 12.0,
      minDistance: 10.0,
      rollSensitivity: 0.8
    }
  }

  /**
   * 检查是否为已知的飞机类型
   */
  private isKnownAircraftType(identifier: string): boolean {
    const aircraftKeywords = [
      'f18', 'f16', 'f22', 'f35', 'su27', 'su35', 'j20', 'j10',
      'mig29', 'eurofighter', 'rafale', 'typhoon', 'hornet',
      'falcon', 'raptor', 'lightning', 'flanker', 'fulcrum'
    ]

    return aircraftKeywords.some(keyword => identifier.includes(keyword))
  }

  /**
   * 获取飞机翼展（根据飞机类型）
   */
  private getAircraftWingSpan(modelId: string): number {
    // 根据模型ID或类型确定翼展
    // 这里可以根据实际的飞机模型数据库来设置
    const aircraftTypes: { [key: string]: number } = {
      f18: 12.3, // F-18 Super Hornet
      f16: 9.8, // F-16 Fighting Falcon
      f22: 13.6, // F-22 Raptor
      f35: 10.7, // F-35 Lightning II
      su27: 14.7, // Su-27 Flanker
      su35: 15.3, // Su-35 Flanker-E
      j20: 13.0, // J-20 Mighty Dragon
      j10: 9.75, // J-10 Vigorous Dragon
      mig29: 11.4, // MiG-29 Fulcrum
      eurofighter: 10.95, // Eurofighter Typhoon
      rafale: 10.8, // Dassault Rafale
      default: 12.0, // 默认翼展
    }

    // 从modelId中提取飞机类型
    const modelIdLower = modelId.toLowerCase()
    for (const [type, wingspan] of Object.entries(aircraftTypes)) {
      if (modelIdLower.includes(type)) {
        return wingspan
      }
    }

    // 如果没有匹配到特定类型，返回默认值
    return aircraftTypes.default
  }

  /**
   * 更新模型飘带配置
   */
  public updateRibbonConfig(
    modelId: string,
    config: {
      color?: Cesium.Color
      wingSpan?: number
      maxPoints?: number
      minDistance?: number
      rollSensitivity?: number
    }
  ): void {
    // 先移除现有效果
    this.removeRibbonEffect(modelId)

    // 延迟重新添加，确保清理完成
    setTimeout(() => {
      const entity = this.viewer.entities.getById(modelId)
      if (entity) {
        // 获取默认参数
        const defaultParams = this.getRibbonParams(modelId)

        const ribbonConfig = {
          type: 7 as const,
          modelId: modelId,
          state: 0 as const,
          color: config.color || Cesium.Color.CYAN.withAlpha(0.8),
          customParams: {
            maxPoints: config.maxPoints || this.config.maxTrailPoints,
            wingSpan: config.wingSpan || defaultParams.wingSpan,
            minDistance: config.minDistance || defaultParams.minDistance,
            fadeAlpha: 0.1,
            rollSensitivity: config.rollSensitivity || defaultParams.rollSensitivity,
          },
        }

        this.effectManager.createEffect(ribbonConfig, modelId, 7)
      }
    }, 100)
  }

  /**
   * 启用或禁用默认飘带效果
   */
  public setRibbonByDefault(enabled: boolean): void {
    this.config.enableRibbonByDefault = enabled
    console.log(`Ribbon effects by default: ${enabled ? 'enabled' : 'disabled'}`)
  }

  /**
   * 为所有现有模型添加飘带效果
   */
  public addRibbonToAllModels(): void {
    this.viewer.entities.values.forEach(entity => {
      if (entity.model && entity.id) {
        // 检查是否已经有飘带效果
        const existingEffect = this.effectManager.effects?.get(`${entity.id}_7`)
        if (!existingEffect) {
          // 尝试从实体属性中获取模型信息
          const modelType = (entity as any).modelType || 'unknown'
          const modelIcon = (entity as any).modelIcon || entity.id
          const side = (entity as any).side || 'blue'

          this.addRibbonEffect(entity.id, side, modelType, modelIcon)
        }
      }
    })
  }

  /**
   * 移除所有模型的飘带效果
   */
  public removeRibbonFromAllModels(): void {
    this.viewer.entities.values.forEach(entity => {
      if (entity.model && entity.id) {
        this.removeRibbonEffect(entity.id)
      }
    })
  }
}
