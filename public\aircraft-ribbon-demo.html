<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞机飘带效果演示</title>
    <script src="https://cesium.com/downloads/cesiumjs/releases/1.95/Build/Cesium/Cesium.js"></script>
    <link href="https://cesium.com/downloads/cesiumjs/releases/1.95/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <style>
        html,
        body,
        #cesiumContainer {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .demo-panel {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(42, 42, 42, 0.8);
            padding: 15px;
            border-radius: 5px;
            color: white;
            font-family: sans-serif;
            z-index: 1000;
            min-width: 250px;
        }

        .demo-panel h3 {
            margin: 0 0 15px 0;
            color: #48b;
        }

        .demo-panel button {
            background: #48b;
            color: white;
            border: none;
            padding: 8px 15px;
            margin: 5px 5px 5px 0;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .demo-panel button:hover {
            background: #369;
        }

        .demo-panel button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .config-group {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #666;
            border-radius: 3px;
        }

        .config-group label {
            display: block;
            margin: 5px 0;
            font-size: 12px;
        }

        .config-group input[type="range"] {
            width: 100%;
            margin: 5px 0;
        }

        .config-group input[type="color"] {
            width: 50px;
            height: 25px;
            border: none;
            border-radius: 3px;
        }

        .status {
            margin-top: 10px;
            padding: 5px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 3px;
            font-size: 11px;
        }
    </style>
</head>

<body>
    <div id="cesiumContainer"></div>

    <div class="demo-panel">
        <h3>🛩️ 飞机飘带效果演示</h3>

        <div>
            <button id="singleDemo">单机演示</button>
            <button id="formationDemo">编队演示</button>
            <button id="spiralDemo">螺旋飞行</button>
            <button id="rollDemo">翻滚演示</button>
            <button id="scaleTest">缩放测试</button>
            <button id="stopDemo">停止演示</button>
        </div>

        <div class="config-group">
            <label>飘带配置</label>
            <label>
                宽度: <span id="widthValue">37</span>
                <input type="range" id="ribbonWidth" min="2" max="40" value="37">
            </label>
            <label>
                长度: <span id="lengthValue">50</span>
                <input type="range" id="ribbonLength" min="20" max="2000" value="50">
            </label>
            <label>
                颜色: <input type="color" id="ribbonColor" value="#00ffff">
            </label>
            <button id="applyConfig">应用配置</button>
        </div>

        <div class="config-group">
            <label>相机控制</label>
            <button id="followAircraft">跟踪飞机</button>
            <button id="freeCamera">自由视角</button>
            <button id="topView">俯视图</button>
        </div>

        <div class="status" id="status">
            状态: 就绪
        </div>
    </div>

    <script>
        // 初始化Cesium
        Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.frpOhwF27cIuEfRqTwS8_O0nhiQlCPG9klt0_AvNCZY'; // 替换为实际的token

        const viewer = new Cesium.Viewer('cesiumContainer', {
            terrainProvider: Cesium.createWorldTerrain(),
            homeButton: false,
            sceneModePicker: false,
            baseLayerPicker: false,
            navigationHelpButton: false,
            animation: true,
            timeline: true
        });

        // 模拟飞机飘带效果管理器
        class SimpleRibbonManager {
            constructor(viewer) {
                this.viewer = viewer;
                this.ribbons = new Map();
                this.config = {
                    maxPoints: 20,
                    baseWidth: 37.0, // 默认飘带宽度（像素）
                    wingSpan: 25.0, // 翼展宽度（米）
                    color: Cesium.Color.CYAN.withAlpha(0.8),
                    minDistance: 40.0, // 增加距离间隔，减少点密度
                    crossSectionInterval: 2 // 每隔几个点添加一条横截面线
                };
            }

            addRibbon(aircraftId) {
                const aircraft = this.viewer.entities.getById(aircraftId);
                if (!aircraft) {
                    console.error(`Aircraft not found: ${aircraftId}`);
                    return;
                }

                console.log(`Adding ribbon for aircraft: ${aircraftId}`);

                const ribbonData = {
                    positions: [],
                    rollAngles: [],
                    lastPosition: null,
                    lastOrientation: null,
                    entity: null
                };

                // 立即添加初始点
                const currentTime = this.viewer.clock.currentTime;
                const currentPosition = aircraft.position.getValue(currentTime);
                const currentOrientation = aircraft.orientation ? aircraft.orientation.getValue(currentTime) : null;

                if (currentPosition) {
                    const initialPoint = currentPosition.clone();
                    const initialRoll = this.extractRollAngle(currentOrientation);

                    // 添加初始点
                    ribbonData.positions.push(initialPoint);
                    ribbonData.rollAngles.push(initialRoll);

                    // 添加第二个点（稍微偏移）以确保飘带能显示
                    const offset = new Cesium.Cartesian3(-10, 0, 0); // 向后10米
                    const secondPoint = Cesium.Cartesian3.add(initialPoint, offset, new Cesium.Cartesian3());
                    ribbonData.positions.push(secondPoint);
                    ribbonData.rollAngles.push(initialRoll);

                    ribbonData.lastPosition = currentPosition.clone();
                    ribbonData.lastOrientation = currentOrientation ? currentOrientation.clone() : null;

                    console.log(`Added initial ribbon points for ${aircraftId}, total: ${ribbonData.positions.length}`);
                }

                // 重新设计数据结构以支持翼展历史姿态
                ribbonData.wingTrailPoints = []; // 存储翼展轨迹点
                ribbonData.entities = []; // 存储多个实体

                // 创建中心轨迹线（飞机中心路径）
                const centerTrail = this.viewer.entities.add({
                    polyline: {
                        positions: new Cesium.CallbackProperty(() => {
                            return this.getCenterTrailPositions(aircraftId);
                        }, false),
                        width: 5,
                        material: new Cesium.PolylineGlowMaterialProperty({
                            glowPower: 0.3,
                            color: this.config.color
                        }),
                        clampToGround: false
                    }
                });

                // 创建左翼轨迹线
                const leftWingTrail = this.viewer.entities.add({
                    polyline: {
                        positions: new Cesium.CallbackProperty(() => {
                            return this.getLeftWingTrailPositions(aircraftId);
                        }, false),
                        width: 3,
                        material: new Cesium.PolylineGlowMaterialProperty({
                            glowPower: 0.2,
                            color: this.config.color.withAlpha(0.7)
                        }),
                        clampToGround: false
                    }
                });

                // 创建右翼轨迹线
                const rightWingTrail = this.viewer.entities.add({
                    polyline: {
                        positions: new Cesium.CallbackProperty(() => {
                            return this.getRightWingTrailPositions(aircraftId);
                        }, false),
                        width: 3,
                        material: new Cesium.PolylineGlowMaterialProperty({
                            glowPower: 0.2,
                            color: this.config.color.withAlpha(0.7)
                        }),
                        clampToGround: false
                    }
                });

                ribbonData.entities = [centerTrail, leftWingTrail, rightWingTrail];

                // 添加翼展横截面线实体（将在updateWingTrailData中动态创建）
                ribbonData.crossSectionEntities = [];

                console.log(`Wing trail entities created for ${aircraftId}`);
                this.ribbons.set(aircraftId, ribbonData);
            }

            updateWingTrailData(aircraftId) {
                const ribbonData = this.ribbons.get(aircraftId);
                if (!ribbonData) return;

                const aircraft = this.viewer.entities.getById(aircraftId);
                if (!aircraft || !aircraft.position) return;

                const currentTime = this.viewer.clock.currentTime;
                const currentPosition = aircraft.position.getValue(currentTime);
                const currentOrientation = aircraft.orientation ? aircraft.orientation.getValue(currentTime) : null;

                if (!currentPosition) return;

                // 检查是否需要添加新的翼展轨迹点
                if (!ribbonData.lastPosition ||
                    Cesium.Cartesian3.distance(currentPosition, ribbonData.lastPosition) > this.config.minDistance) {

                    // 计算翼展端点
                    const wingPoints = this.calculateWingPoints(currentPosition, currentOrientation);

                    // 添加新的翼展轨迹点
                    const trailPoint = {
                        center: currentPosition.clone(),
                        leftWing: wingPoints.leftWing,
                        rightWing: wingPoints.rightWing,
                        orientation: currentOrientation ? currentOrientation.clone() : null,
                        timestamp: Cesium.JulianDate.now()
                    };

                    ribbonData.wingTrailPoints.push(trailPoint);
                    ribbonData.lastPosition = currentPosition.clone();
                    ribbonData.lastOrientation = currentOrientation ? currentOrientation.clone() : null;

                    // 限制轨迹点数量
                    if (ribbonData.wingTrailPoints.length > this.config.maxPoints) {
                        ribbonData.wingTrailPoints.shift();
                        // 同时移除对应的横截面线
                        if (ribbonData.crossSectionEntities.length > 0) {
                            const oldEntity = ribbonData.crossSectionEntities.shift();
                            this.viewer.entities.remove(oldEntity);
                        }
                    }

                    // 每隔几个点添加一条横截面线来显示翼展姿态
                    if (ribbonData.wingTrailPoints.length % this.config.crossSectionInterval === 0) {
                        this.addWingCrossSection(aircraftId, trailPoint);
                    }

                    if (ribbonData.wingTrailPoints.length <= 3) {
                        console.log(`Added wing trail point for ${aircraftId}, total: ${ribbonData.wingTrailPoints.length}`);
                    }
                }
            }

            // 计算翼展端点位置
            calculateWingPoints(position, orientation) {
                const wingSpan = this.config.wingSpan;

                if (!orientation) {
                    // 如果没有方向信息，使用默认的东西方向
                    const leftOffset = new Cesium.Cartesian3(0, wingSpan / 2, 0);
                    const rightOffset = new Cesium.Cartesian3(0, -wingSpan / 2, 0);

                    return {
                        leftWing: Cesium.Cartesian3.add(position, leftOffset, new Cesium.Cartesian3()),
                        rightWing: Cesium.Cartesian3.add(position, rightOffset, new Cesium.Cartesian3())
                    };
                }

                try {
                    // 创建变换矩阵
                    const transform = Cesium.Transforms.eastNorthUpToFixedFrame(position);
                    const rotationMatrix = Cesium.Matrix3.fromQuaternion(orientation);

                    // 翼展方向（飞机本地坐标系中的Y轴）
                    const leftWingOffset = new Cesium.Cartesian3(0, wingSpan / 2, 0);
                    const rightWingOffset = new Cesium.Cartesian3(0, -wingSpan / 2, 0);

                    // 转换到世界坐标系
                    const leftWorldOffset = Cesium.Matrix3.multiplyByVector(rotationMatrix, leftWingOffset, new Cesium.Cartesian3());
                    const rightWorldOffset = Cesium.Matrix3.multiplyByVector(rotationMatrix, rightWingOffset, new Cesium.Cartesian3());

                    return {
                        leftWing: Cesium.Cartesian3.add(position, leftWorldOffset, new Cesium.Cartesian3()),
                        rightWing: Cesium.Cartesian3.add(position, rightWorldOffset, new Cesium.Cartesian3())
                    };
                } catch (error) {
                    console.warn('Error calculating wing points:', error);
                    return {
                        leftWing: position.clone(),
                        rightWing: position.clone()
                    };
                }
            }

            // 添加翼展横截面线（显示特定时刻的翼展姿态）
            addWingCrossSection(aircraftId, trailPoint) {
                const ribbonData = this.ribbons.get(aircraftId);
                if (!ribbonData) return;

                // 计算透明度（越新的线越不透明）
                const age = ribbonData.crossSectionEntities.length;
                const maxAge = Math.floor(this.config.maxPoints / this.config.crossSectionInterval);
                const alpha = Math.max(0.2, 1.0 - (age / maxAge) * 0.6);

                // 创建横截面线（从左翼到右翼）
                const crossSectionEntity = this.viewer.entities.add({
                    polyline: {
                        positions: [trailPoint.leftWing, trailPoint.rightWing],
                        width: 3,
                        material: new Cesium.PolylineGlowMaterialProperty({
                            glowPower: 0.2,
                            color: this.config.color.withAlpha(alpha)
                        }),
                        clampToGround: false
                    }
                });

                ribbonData.crossSectionEntities.push(crossSectionEntity);
            }

            // 获取中心轨迹线位置
            getCenterTrailPositions(aircraftId) {
                this.updateWingTrailData(aircraftId);
                const ribbonData = this.ribbons.get(aircraftId);
                if (!ribbonData || !ribbonData.wingTrailPoints) return [];

                return ribbonData.wingTrailPoints.map(point => point.center);
            }

            // 获取左翼轨迹线位置
            getLeftWingTrailPositions(aircraftId) {
                const ribbonData = this.ribbons.get(aircraftId);
                if (!ribbonData || !ribbonData.wingTrailPoints) return [];

                return ribbonData.wingTrailPoints.map(point => point.leftWing);
            }

            // 获取右翼轨迹线位置
            getRightWingTrailPositions(aircraftId) {
                const ribbonData = this.ribbons.get(aircraftId);
                if (!ribbonData || !ribbonData.wingTrailPoints) return [];

                return ribbonData.wingTrailPoints.map(point => point.rightWing);
            }

            // 计算飘带起始点（飞机尾部位置）
            calculateRibbonStartPoint(position, orientation) {
                if (!orientation) return position.clone();

                try {
                    // 创建变换矩阵
                    const transform = Cesium.Transforms.eastNorthUpToFixedFrame(position);
                    const rotationMatrix = Cesium.Matrix3.fromQuaternion(orientation);

                    // 飞机尾部偏移（相对于飞机本地坐标系）
                    const tailOffset = new Cesium.Cartesian3(-20, 0, -2); // 向后20米，向下2米

                    // 将偏移转换到世界坐标系
                    const worldOffset = Cesium.Matrix3.multiplyByVector(
                        rotationMatrix,
                        tailOffset,
                        new Cesium.Cartesian3()
                    );

                    // 应用到世界坐标
                    const worldPosition = Cesium.Matrix4.multiplyByPoint(
                        transform,
                        worldOffset,
                        new Cesium.Cartesian3()
                    );

                    return Cesium.Cartesian3.add(
                        position,
                        worldPosition,
                        new Cesium.Cartesian3()
                    );
                } catch (error) {
                    console.error('Error calculating ribbon start point:', error);
                    return position.clone();
                }
            }

            // 计算GLB模型在屏幕上的像素宽度
            calculateModelScreenWidth(entity, position, orientation) {
                try {
                    // 获取模型的缩放和最小像素尺寸
                    const model = entity.model;
                    if (!model) {
                        return this.config.baseWidth;
                    }

                    const scale = model.scale ? model.scale.getValue(this.viewer.clock.currentTime) : 1.0;
                    const minimumPixelSize = model.minimumPixelSize ? model.minimumPixelSize.getValue(this.viewer.clock.currentTime) : 0;

                    // 计算相机到模型的距离
                    const cameraPosition = this.viewer.camera.position;
                    const distance = Cesium.Cartesian3.distance(cameraPosition, position);

                    // 估算模型的实际尺寸（基于翼展数据）
                    const wingSpanMeters = this.getEntityWingSpan(entity.id || '');

                    // 将世界坐标尺寸转换为屏幕像素尺寸
                    const canvas = this.viewer.scene.canvas;

                    // 获取视角，处理不同类型的frustum
                    let fov = Math.PI / 3; // 默认60度视角
                    const frustum = this.viewer.camera.frustum;
                    if (frustum.fov !== undefined) {
                        fov = frustum.fov;
                    }

                    const pixelsPerMeter = (canvas.clientHeight / 2) / (distance * Math.tan(fov / 2));

                    // 计算屏幕上的翼展像素宽度
                    let screenWidth = wingSpanMeters * pixelsPerMeter * scale;

                    // 应用最小像素尺寸限制
                    if (minimumPixelSize > 0) {
                        screenWidth = Math.max(screenWidth, minimumPixelSize);
                    }

                    // 限制最大和最小宽度
                    return Math.max(2, Math.min(screenWidth, 200));
                } catch (error) {
                    console.warn('Error calculating model screen width:', error);
                    return this.config.baseWidth;
                }
            }

            // 获取实体的翼展数据
            getEntityWingSpan(entityId) {
                // 飞机类型翼展数据库
                const aircraftTypes = {
                    'f18': 12.3,      // F-18 Super Hornet
                    'f16': 9.8,       // F-16 Fighting Falcon
                    'f22': 13.6,      // F-22 Raptor
                    'f35': 10.7,      // F-35 Lightning II
                    'su27': 14.7,     // Su-27 Flanker
                    'su35': 15.3,     // Su-35 Flanker-E
                    'j20': 13.0,      // J-20 Mighty Dragon
                    'j10': 9.75,      // J-10 Vigorous Dragon
                    'mig29': 11.4,    // MiG-29 Fulcrum
                    'eurofighter': 10.95, // Eurofighter Typhoon
                    'rafale': 10.8,   // Dassault Rafale
                    'cesium_air': 8.5, // Cesium Air 默认模型
                    'demo_aircraft': 10.7, // 演示飞机使用F35数据
                    'formation': 8.5,  // 编队飞机使用Cesium Air数据
                    'spiral': 10.7,   // 螺旋飞行使用F35数据
                    'roll': 10.7,     // 翻滚演示使用F35数据
                    'default': 12.0   // 默认翼展
                };

                // 从entityId中提取飞机类型
                const entityIdLower = entityId.toLowerCase();
                for (const [type, wingspan] of Object.entries(aircraftTypes)) {
                    if (entityIdLower.includes(type)) {
                        return wingspan;
                    }
                }

                // 如果没有匹配到特定类型，返回默认值
                return aircraftTypes.default;
            }

            // 提取四元数中的翻滚角度
            extractRollAngle(orientation) {
                if (!orientation) return 0;

                // 将四元数转换为欧拉角中的翻滚角
                const roll = Math.atan2(
                    2 * (orientation.w * orientation.x + orientation.y * orientation.z),
                    1 - 2 * (orientation.x * orientation.x + orientation.y * orientation.y)
                );

                return roll;
            }

            // 计算飘带宽度（基于GLB模型实际渲染宽度和翻滚角度）
            calculateRibbonWidth(aircraftId) {
                // 暂时返回固定宽度进行测试
                return 15.0;

                const ribbonData = this.ribbons.get(aircraftId);
                if (!ribbonData) {
                    return this.config.baseWidth;
                }

                // 获取模型实体
                const entity = this.viewer.entities.getById(aircraftId);
                if (!entity || !entity.position) {
                    return this.config.baseWidth;
                }

                try {
                    const currentTime = this.viewer.clock.currentTime;
                    const position = entity.position.getValue(currentTime);
                    const orientation = entity.orientation ? entity.orientation.getValue(currentTime) : null;

                    if (!position) {
                        return this.config.baseWidth;
                    }

                    // 计算模型在屏幕上的像素宽度
                    const modelScreenWidth = this.calculateModelScreenWidth(entity, position, orientation);

                    // 获取翻滚角度影响
                    let rollFactor = 1.0;
                    if (ribbonData.rollAngles.length > 0) {
                        const latestRollAngle = ribbonData.rollAngles[ribbonData.rollAngles.length - 1];
                        rollFactor = Math.abs(Math.cos(latestRollAngle));
                    }

                    // 飘带宽度 = 模型屏幕宽度 * 翻滚因子 * 调整系数
                    const ribbonWidth = modelScreenWidth * rollFactor * 0.8; // 0.8是调整系数，使飘带略窄于模型

                    // 调试信息（仅在前几次计算时输出）
                    if (Math.random() < 0.01) { // 1%的概率输出调试信息
                        console.log(`Ribbon width calculation for ${aircraftId}:`, {
                            modelScreenWidth: modelScreenWidth.toFixed(2),
                            rollFactor: rollFactor.toFixed(2),
                            finalWidth: ribbonWidth.toFixed(2),
                            distance: Cesium.Cartesian3.distance(this.viewer.camera.position, position).toFixed(0) + 'm'
                        });
                    }

                    return Math.max(this.config.baseWidth, ribbonWidth);
                } catch (error) {
                    console.warn('Error calculating ribbon width:', error);
                    return this.config.baseWidth;
                }
            }

            removeRibbon(aircraftId) {
                const ribbonData = this.ribbons.get(aircraftId);
                if (ribbonData) {
                    // 移除主要轨迹线实体
                    if (ribbonData.entities) {
                        ribbonData.entities.forEach(entity => {
                            this.viewer.entities.remove(entity);
                        });
                    }

                    // 移除横截面线实体
                    if (ribbonData.crossSectionEntities) {
                        ribbonData.crossSectionEntities.forEach(entity => {
                            this.viewer.entities.remove(entity);
                        });
                    }

                    // 兼容旧版本
                    if (ribbonData.entity) {
                        this.viewer.entities.remove(ribbonData.entity);
                    }
                }
                this.ribbons.delete(aircraftId);
            }

            updateConfig(newConfig) {
                // 处理旧的width参数，转换为baseWidth
                if (newConfig.width !== undefined) {
                    newConfig.baseWidth = newConfig.width;
                    delete newConfig.width;
                }

                this.config = { ...this.config, ...newConfig };

                // 更新所有现有飘带
                this.ribbons.forEach((ribbonData, aircraftId) => {
                    if (ribbonData.entity && ribbonData.entity.polyline) {
                        // 宽度现在是动态计算的，不需要直接设置
                        ribbonData.entity.polyline.material = new Cesium.PolylineGlowMaterialProperty({
                            glowPower: 0.3,
                            color: this.config.color
                        });
                    }
                });
            }

            clear() {
                this.ribbons.forEach((ribbonData, aircraftId) => {
                    this.removeRibbon(aircraftId);
                });
            }
        }

        // 初始化飘带管理器
        const ribbonManager = new SimpleRibbonManager(viewer);
        let currentDemo = null;

        // 创建飞行路径
        function createFlightPath(waypoints, startTime) {
            const property = new Cesium.SampledPositionProperty();

            waypoints.forEach(point => {
                const time = Cesium.JulianDate.addSeconds(startTime, point.time, new Cesium.JulianDate());
                const position = Cesium.Cartesian3.fromDegrees(point.lon, point.lat, point.alt);
                property.addSample(time, position);
            });

            property.setInterpolationOptions({
                interpolationDegree: 2,
                interpolationAlgorithm: Cesium.HermitePolynomialApproximation
            });

            return property;
        }

        // 单机演示
        function createSingleDemo() {
            stopCurrentDemo();

            const startTime = Cesium.JulianDate.now();
            const waypoints = [
                { lon: 116.0, lat: 39.0, alt: 5000, time: 0 },
                { lon: 116.3, lat: 39.2, alt: 6000, time: 30 },
                { lon: 116.6, lat: 39.1, alt: 7000, time: 60 },
                { lon: 116.9, lat: 38.9, alt: 6000, time: 90 },
                { lon: 117.2, lat: 38.7, alt: 5000, time: 120 }
            ];

            const flightPath = createFlightPath(waypoints, startTime);

            const aircraft = viewer.entities.add({
                id: 'demo_aircraft',
                name: '演示飞机',
                position: flightPath,
                orientation: new Cesium.VelocityOrientationProperty(flightPath),
                model: {
                    uri: './F35.glb',
                    minimumPixelSize: 64,
                    maximumScale: 200,
                    scale: 2.0
                }
            });

            ribbonManager.addRibbon('demo_aircraft');
            setupAnimation(startTime, 120);
            viewer.trackedEntity = aircraft;

            currentDemo = 'single';
            updateStatus('单机演示运行中');
        }

        // 编队演示
        function createFormationDemo() {
            stopCurrentDemo();

            const startTime = Cesium.JulianDate.now();
            const baseWaypoints = [
                { lon: 116.0, lat: 39.0, alt: 6000, time: 0 },
                { lon: 116.4, lat: 39.3, alt: 7000, time: 40 },
                { lon: 116.8, lat: 39.1, alt: 6500, time: 80 },
                { lon: 117.2, lat: 38.8, alt: 6000, time: 120 }
            ];

            const formations = [
                { id: 'leader', offset: { lon: 0, lat: 0, alt: 0 }, color: Cesium.Color.RED },
                { id: 'wingman1', offset: { lon: -0.01, lat: -0.005, alt: -50 }, color: Cesium.Color.BLUE },
                { id: 'wingman2', offset: { lon: 0.01, lat: -0.005, alt: -50 }, color: Cesium.Color.GREEN }
            ];

            formations.forEach(formation => {
                const waypoints = baseWaypoints.map(wp => ({
                    lon: wp.lon + formation.offset.lon,
                    lat: wp.lat + formation.offset.lat,
                    alt: wp.alt + formation.offset.alt,
                    time: wp.time
                }));

                const flightPath = createFlightPath(waypoints, startTime);

                const aircraft = viewer.entities.add({
                    id: `formation_${formation.id}`,
                    name: `编队 ${formation.id}`,
                    position: flightPath,
                    orientation: new Cesium.VelocityOrientationProperty(flightPath),
                    model: {
                        uri: './f35.glb',
                        minimumPixelSize: 48,
                        maximumScale: 150,
                        scale: 1.5,
                        color: formation.color
                    }
                });

                // 为每架飞机添加不同颜色的飘带
                const originalColor = ribbonManager.config.color;
                ribbonManager.config.color = formation.color.withAlpha(0.8);
                ribbonManager.addRibbon(`formation_${formation.id}`);
                ribbonManager.config.color = originalColor;
            });

            setupAnimation(startTime, 120);
            viewer.trackedEntity = viewer.entities.getById('formation_leader');

            currentDemo = 'formation';
            updateStatus('编队演示运行中');
        }

        // 螺旋飞行演示
        function createSpiralDemo() {
            stopCurrentDemo();

            const startTime = Cesium.JulianDate.now();
            const center = { lon: 116.0, lat: 39.0, alt: 5000 };
            const waypoints = [];

            for (let i = 0; i <= 60; i++) {
                const t = i / 60;
                const angle = t * 4 * Math.PI;
                const radius = 0.02 * (1 - t * 0.5);

                waypoints.push({
                    lon: center.lon + radius * Math.cos(angle),
                    lat: center.lat + radius * Math.sin(angle),
                    alt: center.alt + t * 3000,
                    time: t * 90
                });
            }

            const flightPath = createFlightPath(waypoints, startTime);

            const aircraft = viewer.entities.add({
                id: 'spiral_aircraft',
                name: '螺旋飞行',
                position: flightPath,
                orientation: new Cesium.VelocityOrientationProperty(flightPath),
                model: {
                    uri: './F35.glb',
                    minimumPixelSize: 64,
                    maximumScale: 200,
                    scale: 2.0,
                    color: Cesium.Color.GOLD
                }
            });

            ribbonManager.config.color = Cesium.Color.GOLD.withAlpha(0.8);
            ribbonManager.addRibbon('spiral_aircraft');

            setupAnimation(startTime, 90);
            viewer.trackedEntity = aircraft;

            currentDemo = 'spiral';
            updateStatus('螺旋飞行演示运行中');
        }

        // 翻滚演示
        function createRollDemo() {
            stopCurrentDemo();

            const startTime = Cesium.JulianDate.now();

            // 创建带翻滚动作的飞行路径
            const waypoints = [
                { lon: 116.0, lat: 39.0, alt: 6000, time: 0 },
                { lon: 116.3, lat: 39.1, alt: 6000, time: 20 },
                { lon: 116.6, lat: 39.2, alt: 6000, time: 40 },
                { lon: 116.9, lat: 39.1, alt: 6000, time: 60 },
                { lon: 117.2, lat: 39.0, alt: 6000, time: 80 },
                { lon: 117.5, lat: 38.9, alt: 6000, time: 100 }
            ];

            const flightPath = createFlightPath(waypoints, startTime);

            // 创建自定义方向属性，包含翻滚动作
            const orientationProperty = new Cesium.SampledProperty(Cesium.Quaternion);

            waypoints.forEach((point, index) => {
                const time = Cesium.JulianDate.addSeconds(startTime, point.time, new Cesium.JulianDate());

                // 计算基础方向（基于速度）
                let heading = 0;
                if (index < waypoints.length - 1) {
                    const nextPoint = waypoints[index + 1];
                    heading = Math.atan2(nextPoint.lon - point.lon, nextPoint.lat - point.lat);
                }

                // 添加翻滚角度：在特定时间段进行360度翻滚
                let roll = 0;
                if (point.time >= 20 && point.time <= 60) {
                    // 在20-60秒之间进行翻滚
                    const rollProgress = (point.time - 20) / 40; // 0到1的进度
                    roll = rollProgress * Math.PI * 4; // 两个完整翻滚
                }

                const orientation = Cesium.Transforms.headingPitchRollQuaternion(
                    Cesium.Cartesian3.fromDegrees(point.lon, point.lat, point.alt),
                    new Cesium.HeadingPitchRoll(heading, 0, roll)
                );

                orientationProperty.addSample(time, orientation);
            });

            const aircraft = viewer.entities.add({
                id: 'roll_aircraft',
                name: '翻滚演示',
                position: flightPath,
                orientation: orientationProperty,
                model: {
                    uri: './F35.glb',
                    minimumPixelSize: 64,
                    maximumScale: 200,
                    scale: 2.0,
                    color: Cesium.Color.ORANGE
                }
            });

            ribbonManager.config.color = Cesium.Color.ORANGE.withAlpha(0.8);
            ribbonManager.addRibbon('roll_aircraft');

            setupAnimation(startTime, 100);
            viewer.trackedEntity = aircraft;

            currentDemo = 'roll';
            updateStatus('翻滚演示运行中 - 观察飘带宽度变化');
        }

        // 停止当前演示
        function stopCurrentDemo() {
            viewer.entities.removeAll();
            ribbonManager.clear();
            viewer.trackedEntity = undefined;
            viewer.clock.shouldAnimate = false;
            currentDemo = null;
            updateStatus('演示已停止');
        }

        // 设置动画
        function setupAnimation(startTime, duration) {
            const endTime = Cesium.JulianDate.addSeconds(startTime, duration, new Cesium.JulianDate());

            viewer.clock.startTime = startTime.clone();
            viewer.clock.stopTime = endTime.clone();
            viewer.clock.currentTime = startTime.clone();
            viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP;
            viewer.clock.multiplier = 1;
            viewer.clock.shouldAnimate = true;
        }

        // 更新状态
        function updateStatus(message) {
            document.getElementById('status').textContent = `状态: ${message}`;
        }

        // 缩放测试演示
        function createScaleTest() {
            stopCurrentDemo();

            const startTime = Cesium.JulianDate.now();

            // 创建简单的直线飞行路径
            const waypoints = [
                { lon: 116.0, lat: 39.0, alt: 5000, time: 0 },
                { lon: 117.0, lat: 39.0, alt: 5000, time: 60 }
            ];

            const flightPath = createFlightPath(waypoints, startTime);

            // 创建动态缩放属性
            const scaleProperty = new Cesium.CallbackProperty((time) => {
                const elapsedSeconds = Cesium.JulianDate.secondsDifference(time, startTime);
                // 缩放在1.0到4.0之间周期性变化
                return 1.0 + 3.0 * (Math.sin(elapsedSeconds * 0.2) + 1) / 2;
            }, false);

            const aircraft = viewer.entities.add({
                id: 'scale_aircraft',
                name: '缩放测试',
                position: flightPath,
                orientation: new Cesium.VelocityOrientationProperty(flightPath),
                model: {
                    uri: './F35.glb',
                    minimumPixelSize: 32,
                    maximumScale: 500,
                    scale: scaleProperty,
                    color: Cesium.Color.CYAN
                }
            });

            ribbonManager.config.color = Cesium.Color.CYAN.withAlpha(0.8);
            ribbonManager.addRibbon('scale_aircraft');

            setupAnimation(startTime, 60);
            viewer.trackedEntity = aircraft;

            currentDemo = 'scale';
            updateStatus('缩放测试运行中 - 观察飘带宽度随模型缩放变化');
        }

        // 事件监听器
        document.getElementById('singleDemo').addEventListener('click', createSingleDemo);
        document.getElementById('formationDemo').addEventListener('click', createFormationDemo);
        document.getElementById('spiralDemo').addEventListener('click', createSpiralDemo);
        document.getElementById('rollDemo').addEventListener('click', createRollDemo);
        document.getElementById('scaleTest').addEventListener('click', createScaleTest);
        document.getElementById('stopDemo').addEventListener('click', stopCurrentDemo);

        // 配置更新
        document.getElementById('ribbonWidth').addEventListener('input', function (e) {
            document.getElementById('widthValue').textContent = e.target.value;
        });

        document.getElementById('ribbonLength').addEventListener('input', function (e) {
            document.getElementById('lengthValue').textContent = e.target.value;
        });

        document.getElementById('applyConfig').addEventListener('click', function () {
            const width = parseFloat(document.getElementById('ribbonWidth').value);
            const maxPoints = parseInt(document.getElementById('ribbonLength').value);
            const colorHex = document.getElementById('ribbonColor').value;
            const color = Cesium.Color.fromCssColorString(colorHex).withAlpha(0.8);

            ribbonManager.updateConfig({
                width: width,
                maxPoints: maxPoints,
                color: color
            });

            updateStatus('配置已更新');
        });

        // 相机控制
        document.getElementById('followAircraft').addEventListener('click', function () {
            if (currentDemo) {
                let aircraftId = 'demo_aircraft';
                if (currentDemo === 'formation') aircraftId = 'formation_leader';
                else if (currentDemo === 'spiral') aircraftId = 'spiral_aircraft';
                else if (currentDemo === 'roll') aircraftId = 'roll_aircraft';

                viewer.trackedEntity = viewer.entities.getById(aircraftId);
            }
        });

        document.getElementById('freeCamera').addEventListener('click', function () {
            viewer.trackedEntity = undefined;
        });

        document.getElementById('topView').addEventListener('click', function () {
            viewer.camera.setView({
                destination: Cesium.Cartesian3.fromDegrees(116.5, 39.0, 50000),
                orientation: {
                    heading: 0,
                    pitch: -Cesium.Math.PI_OVER_TWO,
                    roll: 0
                }
            });
        });

        // 初始化完成
        updateStatus('初始化完成，请选择演示');
    </script>
</body>

</html>