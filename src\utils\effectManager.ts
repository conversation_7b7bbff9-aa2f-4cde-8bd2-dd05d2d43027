// 创建和销毁(静态特效的更新)在本文件中通过websocket执行  绑定到实体上面的特效的更新通过实体位置的移动执行

// import * as Cesium from 'cesium'

// 特效类型定义
export type EffectType =
  | 0 //'communicationLine' // 通信线效果
  | 1 //'scan' // 扫描\探测
  | 2 // 'fire' // 交火
  | 3 // 'tadiga' // 干扰
  | 4 // 'renderExplosionEffect' // 爆炸效果
  | 5 // '' // 平台日志
  | 6 // 'explosion' // 传感器信息
  | 7 // 'aircraftRibbon' // 飞机飘带效果
// | 'trail' // 轨迹效果
// 探测、通信、任务、开火、干扰线
// 特效配置接口
export interface EffectConfig {
  type: EffectType
  position?: {
    lo: number // 经度
    la: number // 纬度
    al: number // 高度
  }
  modelId: string // 绑定的模型ID
  target?: string // 目标模型ID（用于通信线）
  offset?: {
    // 相对于模型的偏移
    x: number
    y: number
    z: number
  }
  color?: Cesium.Color // 特效颜色
  customParams?: any // 自定义参数
  state: 0 | 1 // 0 创建 1销毁
}

// 特效实例接口
interface EffectInstance {
  id: string
  type: EffectType
  entity?: Cesium.Entity
  primitive?: Cesium.Primitive
  graphic?: any
  config: EffectConfig
  position: Cesium.Cartesian3 | number[]
  isActive?: boolean
  ribbonData?: {
    positions: Cesium.Cartesian3[]
    maxPoints: number
    lastPosition?: Cesium.Cartesian3
    lastOrientation?: Cesium.Quaternion
    wingSpan: number
    rollAngles: number[] // 存储每个点对应的翻滚角度
  }
}

export class EffectManager {
  private viewer: Cesium.Viewer
  private effects: Map<string, EffectInstance>
  private ws: WebSocket | null = null
  private wsUrl: string
  private reconnectAttempts: number = 0
  private maxReconnectAttempts: number = 5
  private reconnectDelay: number = 3000
  private effectCounter: number = 0
  translation: any
  rotation: any
  entity: any

  private hpr: Cesium.HeadingPitchRoll | undefined
  private particleSystem: any
  private trs: Cesium.TranslationRotationScale | undefined
  private renderUpdateHandler: (() => void) | null = null
  private viewModel: {
    heading?: number
    emissionRate: number
    gravity: number //设置重力参数
    minimumParticleLife: number
    maximumParticleLife: number
    minimumSpeed: number //粒子发射的最小速度
    maximumSpeed: number //粒子发射的最大速度
    startScale: number
    endScale: number
    particleSize: number
  }

  // 飘带效果配置
  private ribbonConfig = {
    maxPoints: 50, // 飘带最大点数
    wingSpan: 30.0, // 飞机翼展宽度（米）
    ribbonWidth: 2.0, // 飘带基础宽度（像素）
    color: Cesium.Color.CYAN.withAlpha(0.8),
    updateInterval: 100, // 更新间隔(ms)
    minDistance: 10.0, // 最小距离阈值
    fadeAlpha: 0.1, // 渐变透明度
    rollSensitivity: 1.0, // 翻滚角度敏感度
  }
  constructor(viewer: Cesium.Viewer, wsUrl: string) {
    this.viewer = viewer
    this.wsUrl = wsUrl
    this.effects = new Map()
    this.viewModel = {
      emissionRate: 5,
      gravity: 9.0, //设置重力参数
      minimumParticleLife: 1,
      maximumParticleLife: 12,
      minimumSpeed: 1.0, //粒子发射的最小速度
      maximumSpeed: 2.0, //粒子发射的最大速度
      startScale: 1.0,
      endScale: 5.0,
      particleSize: 25.0,
    }
    this.translation = new Cesium.Cartesian3()
    this.rotation = new Cesium.Quaternion()
    this.hpr = new Cesium.HeadingPitchRoll()
    this.trs = new Cesium.TranslationRotationScale()
    this.entity = this.viewer.entities.add({
      //选择粒子放置的坐标
      // position: Cesium.Cartesian3.fromDegrees(this._longitude, this._latitude, this._altitude),
      position: Cesium.Cartesian3.fromDegrees(110, 28, 2600),
    })
    this.initWebSocket()

    // 添加场景渲染事件监听
    this.renderUpdateHandler = () => {
      this.updateEffects()
    }
    this.viewer.scene.preRender.addEventListener(this.renderUpdateHandler)
  }

  /**
   * 初始化WebSocket连接
   */
  private initWebSocket(): void {
    try {
      this.ws = new WebSocket(this.wsUrl)

      this.ws.onopen = () => {
        console.log('特效WebSocket连接已建立')
        this.reconnectAttempts = 0
      }

      this.ws.onmessage = event => {
        this.handleWebSocketMessage(event)
      }

      this.ws.onclose = () => {
        console.log('特效WebSocket连接已关闭')
        this.attemptReconnect()
      }

      this.ws.onerror = error => {
        console.error('特效WebSocket错误:', error)
      }
    } catch (error) {
      console.error('初始化特效WebSocket失败:', error)
      this.attemptReconnect()
    }
  }

  /**
   * 尝试重新连接WebSocket
   */
  private attemptReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(
        `尝试重新连接特效WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`
      )

      setTimeout(() => {
        this.initWebSocket()
      }, this.reconnectDelay)
    } else {
      console.error('特效WebSocket重连失败，已达到最大重试次数')
    }
  }

  /**
   * 处理WebSocket消息
   */
  private handleWebSocketMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data)
      // if (!data || !data.type) {
      //   console.warn('无效的特效消息格式')
      //   return
      // }
      this.batchProcessEffects(data)

      // switch (data.type) {
      //   case 'create':
      //     this.(data.effect)
      //     break
      //   // case 'update':
      //   //   this.updateEffect(data.id, data.effect);
      //   //   break;
      //   case 'destroy':
      //     this.destroyEffect(data.id)
      //     break
      //   case 'batch':
      //     this.batchProcessEffects(data.effects)
      //     break
      //   default:
      //     console.warn(`未知的特效消息类型: ${data.type}`)
      // }
    } catch (error) {
      console.error('处理特效WebSocket消息时出错:', error)
    }
  }

  /**
   * 批量处理特效
   */
  private batchProcessEffects(
    effects: { type: EffectType; name: string; param: EffectConfig }[]
  ): void {
    if (!Array.isArray(effects)) {
      console.warn('批量处理特效需要数组格式')
      return
    }

    effects.forEach(item => {
      if (item.type !== 5) {
        if ('on_off' in item.param) {
          switch (item.param.on_off) {
            case true:
              this.createEffect(item.param, item.name, item.type)
              break
          }
        } else {
          switch (item.param.state) {
            case 0:
              this.createEffect(item.param, item.name, item.type)
              break
          }
        }
      }
    })
  }

  /**
   * 创建特效
   */
  public createEffect(
    config: EffectConfig,
    name: string,
    type: EffectType
  ): string {
    // const id = `effect_${this.effectCounter++}`
    const id = name + '_' + type.toString()

    const effect: EffectInstance = {
      id,
      type,
      config: { ...config, modelId: name },
      position: [],
    }
    // if (effect.entity) {
    if (!name) {
      // 如果没有绑定模型，使用配置中的位置
      const position = Cesium.Cartesian3.fromDegrees(
        effect.config.position?.lo || 0,
        effect.config.position?.la || 0,
        effect.config.position?.al || 0
      )
      // effect.entity.position = new Cesium.ConstantPositionProperty(position)
    } else {
      // 从实体集合中获取模型
      const model = this.viewer.entities.getById(name)
      if (!model || !model.position) {
        console.warn(
          `Model ${effect.config.modelId} not found or has no position for effect ${effect.id}`
        )
        return 'false'
      }

      // 获取模型位置并应用偏移
      const modelPosition = model.position.getValue(Cesium.JulianDate.now())
      if (!modelPosition) {
        console.warn(
          `Could not get position for model ${effect.config.modelId}`
        )
        return 'false'
      }

      const offset = effect.config.offset || { x: 0, y: 0, z: 0 }
      const position = new Cesium.Cartesian3(
        modelPosition.x + offset.x,
        modelPosition.y + offset.y,
        modelPosition.z + offset.z
      )
      // effect.position = new Cesium.ConstantPositionProperty(position)
      effect.position = modelPosition
      this.effects.set(id, effect)
      this.renderEffect(effect)
    }
    // }

    return id
  }

  /**
   * 更新特效
   */
  public updateEffect(id: string, config: Partial<EffectConfig>): boolean {
    const effect = this.effects.get(id)
    if (!effect) {
      console.warn(`尝试更新不存在的特效: ${id}`)
      return false
    }

    // 更新配置
    effect.config = { ...effect.config, ...config }

    // 重新渲染特效
    this.destroyEffect(id, false)
    this.renderEffect(effect)

    // 更新特效位置
    if (effect.entity) {
      if (!effect.config.modelId) {
        // 如果没有绑定模型，使用配置中的位置
        const position = Cesium.Cartesian3.fromDegrees(
          effect.config.position?.lo || 0,
          effect.config.position?.la || 0,
          effect.config.position?.al || 0
        )
        effect.entity.position = new Cesium.ConstantPositionProperty(position)
      } else {
        // 从实体集合中获取模型
        const model = this.viewer.entities.getById(effect.config.modelId)
        if (!model || !model.position) {
          console.warn(
            `Model ${effect.config.modelId} not found or has no position for effect ${effect.id}`
          )
          return false
        }

        // 获取模型位置并应用偏移
        const modelPosition = model.position.getValue(Cesium.JulianDate.now())
        if (!modelPosition) {
          console.warn(
            `Could not get position for model ${effect.config.modelId}`
          )
          return false
        }

        const offset = effect.config.offset || { x: 0, y: 0, z: 0 }
        const position = new Cesium.Cartesian3(
          modelPosition.x + offset.x,
          modelPosition.y + offset.y,
          modelPosition.z + offset.z
        )
        effect.entity.position = new Cesium.ConstantPositionProperty(position)
      }
    }

    return true
  }

  /**
   * 销毁特效
   */
  public destroyEffect(id: string, removeFromMap: boolean = true): boolean {
    const effect = this.effects.get(id)
    if (!effect) {
      console.warn(`尝试销毁不存在的特效: ${id}`)
      return false
    }

    // 移除实体或图元
    if (effect.entity) {
      try {
        this.viewer.entities.remove(effect.entity)
        effect.entity = undefined
      } catch (err) {
        console.log(err, '销毁出错')
      }
    }

    if (effect.primitive) {
      this.viewer.scene.primitives.remove(effect.primitive)
      effect.primitive = undefined
    }
    if (effect.graphic) {
      this.viewer.graphicLayer.remove(effect.graphic)
      effect.graphic = undefined
    }

    // 从Map中移除
    if (removeFromMap) {
      this.effects.delete(id)
    }

    return true
  }
  public destroyEffectByModelId(id: string): boolean {
    const prefix = id + '_'
    for (const [effectId, effect] of this.effects.entries()) {
      if (effectId.startsWith(prefix)) {
        const rest = effectId.slice(prefix.length)
        if (!rest.includes('_')) {
          console.log(
            '🚀 ~ EffectManager ~ destroyEffectByModelId ~ effect:',
            effect,
            effectId
          )
          // 移除实体或图元
          if (effect.entity) {
            try {
              this.viewer.entities.remove(effect.entity)
              effect.entity = undefined
            } catch (err) {
              console.log(err, '销毁出错')
            }
          }

          if (effect.primitive) {
            this.viewer.scene.primitives.remove(effect.primitive)
            effect.primitive = undefined
          }
          if (effect.graphic) {
            this.viewer.graphicLayer.remove(effect.graphic)
            effect.graphic = undefined
          }

          this.effects.delete(effectId)
        }
      }
    }
    return true
  }
  /**
   * 渲染特效
   */
  private renderEffect(effect: EffectInstance): void {
    switch (effect.type) {
      case 0:
        this.renderCommunicationLineEffect(effect)
        break
      case 1:
        // this.scanEffect(effect, effect.position)
        break
      // case 2:
      //   this.renderExplosionEffect(effect)
      //   break
      case 3:
        // this.scanEffect(effect, effect.position)
        break
      case 6:
        if (effect.position instanceof Cesium.Cartesian3) {
          this.scanEffect(effect, effect.position)
        }
        break
      case 7:
        this.renderAircraftRibbonEffect(effect)
        break
      default:
        console.warn(`未知的特效类型: ${effect.type}`)
    }

    // 检查特效是否成功渲染
    // setTimeout(() => {
    //   if (effect.primitive) {
    //     console.log('特效图元存在:', effect.primitive)
    //   } else if (effect.entity) {
    //     console.log('特效实体存在:', effect.entity)
    //   } else {
    //     console.warn('特效渲染失败，没有图元或实体')
    //   }
    // }, 100)
  }

  /**
   * 渲染爆炸效果
   */
  private renderExplosionEffect(
    effect: EffectInstance,
    position: Cesium.Cartesian3
  ): void {
    const scale = 500.0 // 显著增加缩放值
    const color = effect.config.color || Cesium.Color.ORANGE

    // 创建爆炸粒子系统
    const model = this.viewer.entities.getById(effect.config.modelId)
    // const modelPosition = model.position.getValue(Cesium.JulianDate.now())
    // const entity = this.viewer.entities.add({
    //   //选择粒子放置的坐标
    //   position: Cesium.Cartesian3.fromDegrees(-90, 38, 6000),
    //   // position: modelPosition,
    // })
    const particleSystem = this.viewer.scene.primitives.add(
      new Cesium.ParticleSystem({
        image: '/effects/fire11.png',
        startScale: 1.0,
        endScale: 3.0,
        minimumParticleLife: 0.5,
        maximumParticleLife: 1.5,
        speed: 10.0,
        // width: 20,
        // height: 20,
        lifetime: 1.0,
        imageSize: new Cesium.Cartesian2(20, 20),
        //主模型参数(位置)
        modelMatrix: this.computeModelMatrix(model, Cesium.JulianDate.now()),
        // 发射器参数
        emitter: new Cesium.SphereEmitter(5.0),
        emissionRate: 30.0,
        emitterModelMatrix: this.computeEmitterModelMatrix(),
        //颜色
        startColor: Cesium.Color.RED.withAlpha(0.8),
        endColor: Cesium.Color.YELLOW.withAlpha(0.0),
        loop: false,
        // forces: [applyGravity]
        bursts: [
          // these burst will occasionally sync to create a multicolored effect
          new Cesium.ParticleBurst({
            time: 0.0,
            minimum: 80,
            maximum: 100,
          }),
          // new Cesium.ParticleBurst({
          //   time: 1.0,
          //   minimum: 500,
          //   maximum: 5000,
          // }),
        ],
      })
    )
    effect.primitive = particleSystem

    this.effects.set(effect.config.modelId, effect)
    // 保存特效引用
    // 设置自动销毁
    // setTimeout(() => {
    //   console.log('销毁爆炸特效')
    //   this.viewer.scene.primitives.remove(particleSystem)
    //   effect.primitive = undefined
    // }, 1000)
  }
  private computeModelMatrix(entity, time) {
    //获取位置
    var position = Cesium.Property.getValueOrUndefined(
      entity.position,
      time,
      new Cesium.Cartesian3()
    )
    if (!Cesium.defined(position)) {
      return undefined
    }
    //获取方向
    var modelMatrix
    var orientation = Cesium.Property.getValueOrUndefined(
      entity.orientation,
      time,
      new Cesium.Quaternion()
    )
    if (!Cesium.defined(orientation)) {
      modelMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(
        position,
        undefined,
        new Cesium.Matrix4()
      )
    } else {
      modelMatrix = Cesium.Matrix4.fromRotationTranslation(
        Cesium.Matrix3.fromQuaternion(orientation, new Cesium.Matrix3()),
        position,
        new Cesium.Matrix4()
      )
    }
    // console.log()
    return modelMatrix
  }
  private computeEmitterModelMatrix() {
    this.hpr = Cesium.HeadingPitchRoll.fromDegrees(
      0.0,
      0.0,
      0.0,
      new Cesium.HeadingPitchRoll()
    )
    var trs = new Cesium.TranslationRotationScale()

    //以modelMatrix(飞机)中心为原点的坐标系的xyz轴位置偏移
    trs.translation = Cesium.Cartesian3.fromElements(
      2.5,
      3.5,
      1.0,
      new Cesium.Cartesian3()
    )
    trs.rotation = Cesium.Quaternion.fromHeadingPitchRoll(
      this.hpr,
      new Cesium.Quaternion()
    )
    return Cesium.Matrix4.fromTranslationRotationScale(
      trs,
      new Cesium.Matrix4()
    )
  }
  /**
   * 渲染扫描效果
   */
  private scanEffect(
    effect: EffectInstance,
    position: Cesium.Cartesian3
  ): void {
    if (effect.config.name !== 'geo_sensor') return
    const model = this.viewer.entities.getById(effect.config.modelId)

    const newOri = this.getOrientations(model)

    const newPosition = this.cartesianToLonLatHeight(position)
    // const angleObj = this.computrRadarScanAngles(
    //   newOri.roll,
    //   newOri.pitch,
    //   newOri.yaw,
    //   60,
    //   120,
    //   -30,
    //   -90
    // )
    const angleObj = {
      startAngle: 0,
      endAngle: 360,
      vStartAngle: -30,
      vEndAngle: -90,
    }
    const radar1 = new GV.RadarGraphic({
      position: new GV.GeoPoint(
        newPosition.lon,
        newPosition.lat,
        newPosition.height
      ),
      startAngle: angleObj.startAngle, //水平起始角度
      endAngle: angleObj.endAngle, //水平终止角
      vStartAngle: angleObj.vStartAngle, //垂直起始角
      vEndAngle: angleObj.vEndAngle, //垂直终止角
      radius: 25000,
      outerColor: 'rgba(0, 204, 0, 0.5)',
      lineColor: 'rgba(0, 204, 0, 0.5)',
      innerColor: 'rgba(0, 204, 0, 0.5)',
      innerAlhpa: 0.5,
      scanColor: 'rgba(242, 226, 75, 0.7)', //扫描线颜色
      scanAngle: 5, //扫描角度
      scanReserve: true, //往复扫描
      showScan: true, //是否显示扫描
      showLine: true, //是否显示外轮廓线
      fillScan: true, //是否填充扫描
    })
    let sensor = new GV.SensorGraphic({
      showScanPlane: false, //是否显示扫描面
      color: `${(window as any).GVJ.URLS.sensorColor.color}`, // 球面颜色
      lineColor: `${(window as any).GVJ.URLS.sensorColor.lineColor}`, // 线颜色
      scanPlaneColor: `${(window as any).GVJ.URLS.sensorColor.scanPlaneColor}`, //扫描面颜色
      position: new GV.GeoPoint(
        newPosition.lon,
        newPosition.lat,
        // newPosition.height
        0
      ),
      xHalfAngle: 180,
      yHalfAngle: 180,
      radius: 25000,
    })
    this.viewer.graphicLayer.add(sensor)
    // this.viewer.graphicLayer.add(radar1)

    this.viewer.clock.onTick.addEventListener(() => {
      const newPosition = this.cartesianToLonLatHeight(
        model.position.getValue(Cesium.JulianDate.now())
      )
      // const newOri = this.getOrientations(model)
      // const angleObj = this.computrRadarScanAngles(
      //   newOri.roll,
      //   newOri.pitch,
      //   newOri.yaw,
      //   60,
      //   120,
      //   -30,
      //   -90
      // )
      sensor.position = new GV.GeoPoint(
        newPosition.lon,
        newPosition.lat,
        newPosition.height
      )
      // radar1.startAngle = angleObj.startAngle //水平起始角度
      // radar1.endAngle = angleObj.endAngle //水平终止角
      // radar1.vStartAngle = angleObj.vStartAngle //垂直起始角
      // radar1.vEndAngle = angleObj.vEndAngle //垂直终止角
    })
    // 保存特效引用
    effect.graphic = sensor
  }
  private getOrientations(entity) {
    const orientation = entity.orientation.getValue(
      this.viewer.clock.currentTime
    )
    const position = entity.position.getValue(this.viewer.clock.currentTime)

    if (!orientation || !position) return null

    // 获取变换矩阵：从 local frame 转为 ENU 世界坐标
    const transform = Cesium.Transforms.eastNorthUpToFixedFrame(position)
    const rotationMatrix = Cesium.Matrix3.fromQuaternion(orientation)

    // 将旋转矩阵从世界系转换为本地 ENU 系
    const localRotation = Cesium.Matrix4.multiplyByMatrix3(
      Cesium.Matrix4.inverseTransformation(transform, new Cesium.Matrix4()),
      rotationMatrix,
      new Cesium.Matrix3()
    )
    const quaternion = Cesium.Quaternion.fromRotationMatrix(localRotation)
    const hpr = Cesium.HeadingPitchRoll.fromQuaternion(quaternion)

    return {
      yaw: Cesium.Math.toDegrees(hpr.heading),
      pitch: Cesium.Math.toDegrees(hpr.pitch),
      roll: Cesium.Math.toDegrees(hpr.roll),
    }
  }

  /**
   * 渲染通信线效果
   */
  private renderCommunicationLineEffect(effect: EffectInstance): void {
    if (!effect.config.modelId || !effect.config.target) {
      console.error('通信线效果需要指定源模型和目标模型ID')
      return
    }

    // 获取源模型和目标模型
    const sourceModel = this.viewer.entities.getById(effect.config.modelId)
    const targetModel = this.viewer.entities.getById(effect.config.target)

    if (!sourceModel || !targetModel) {
      console.error('无法找到指定的模型')
      return
    }

    // 创建通信线实体
    const entity = this.viewer.entities.add({
      polyline: {
        positions: new Cesium.CallbackProperty(() => {
          if (
            !sourceModel ||
            !this.viewer.entities.contains(sourceModel) ||
            !targetModel ||
            !this.viewer.entities.contains(targetModel)
          ) {
            return []
          }
          const sourcePosition = sourceModel.position?.getValue(
            Cesium.JulianDate.now()
          )
          const targetPosition = targetModel.position?.getValue(
            Cesium.JulianDate.now()
          )

          if (!sourcePosition || !targetPosition) {
            return []
          }

          // 添加一些中间点使线条更平滑
          const positions = [sourcePosition]
          const midPoint = Cesium.Cartesian3.lerp(
            sourcePosition,
            targetPosition,
            0.5,
            new Cesium.Cartesian3()
          )

          // 在中间点添加一些高度偏移，使线条呈现弧形
          midPoint.z += 100.0 // 可以根据需要调整高度
          positions.push(midPoint)
          positions.push(targetPosition)

          return positions
        }, false),
        width: 2.0,
        material: new Cesium.PolylineGlowMaterialProperty({
          glowPower: new Cesium.CallbackProperty(() => {
            const time = Cesium.JulianDate.now().secondsOfDay / 1000
            return 0.2 + Math.sin(time) * 0.1
          }, false),
          // glowPower: 0.2,
          color: effect.config.color || Cesium.Color.CYAN,
        }),
      },
    })

    effect.entity = entity
  }

  /**
   * 销毁所有特效
   */
  public destroyAllEffects(): void {
    this.effects.forEach((effect, id) => {
      this.destroyEffect(id)
    })
  }

  /**
   * 关闭WebSocket连接
   */
  public close(): void {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  /**
   * 更新所有活跃的特效
   */
  private updateEffects(): void {
    this.effects.forEach(effect => {
      if (effect.type === 0 && effect.entity) {
        // 获取源模型和目标模型
        const sourceModel = this.viewer.entities.getById(effect.config.modelId)
        const targetModel = this.viewer.entities.getById(effect.config.target)
        if (!sourceModel || !targetModel) {
          this.destroyEffect(effect.id)
          return
        }
      }
      if (effect.isActive && effect.primitive) {
        // 检查粒子系统是否需要更新
        const particleSystem =
          effect.primitive as unknown as Cesium.ParticleSystem
        if (particleSystem && !particleSystem.isDestroyed()) {
          // 更新粒子系统的位置和方向
          const position = effect.config.position
          if (position) {
            // 将经纬度高度转换为笛卡尔坐标
            const cartesian = Cesium.Cartesian3.fromDegrees(
              position.lo,
              position.la,
              position.al
            )
            particleSystem.modelMatrix =
              Cesium.Transforms.eastNorthUpToFixedFrame(cartesian)
          }
        }
      }
    })
  }
  private cartesianToLonLatHeight(cartesian: Cesium.Cartesian3): any {
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian)
    const lon = Cesium.Math.toDegrees(cartographic.longitude)
    const lat = Cesium.Math.toDegrees(cartographic.latitude)
    const height = cartographic.height
    return { lon, lat, height }
  }
  private computrRadarScanAngles1(
    roll: number,
    pitch: number,
    yaw: number,
    horizontalCenterDeg: number,
    horizontalRangeDeg: number,
    verTicalCenteerDeg: number,
    verticalRangeDeg: number
  ): any {
    // 将正西为0°
    const horizontalRad = Cesium.Math.toRadians(horizontalCenterDeg)
    const verticalRad = Cesium.Math.toRadians(verTicalCenteerDeg)
    // 初始方向向量
    const x = Math.cos(verticalRad) * Math.cos(horizontalRad)
    const y = Math.cos(verticalRad) * Math.sin(horizontalRad)
    const z = Math.sin(verticalRad)
    const initialDir = new Cesium.Cartesian3(x, y, z)
    // 姿态旋转矩阵
    const hpr = new Cesium.HeadingPitchRoll(yaw, pitch, roll)
    const rotationMatrix = Cesium.Matrix3.fromHeadingPitchRoll(hpr)
    // 变换方向
    const rotateDir = Cesium.Matrix3.multiplyByVector(
      rotationMatrix,
      initialDir,
      new Cesium.Cartesian3()
    )
    // 水平角
    let horizontalAngle = Math.atan2(rotateDir.y, -rotateDir.x)
    let verticalAngle = Math.atan2(
      rotateDir.z,
      Math.sqrt(rotateDir.x ** 2 + rotateDir.y ** 2)
    )
    // 转为度数
    horizontalAngle = Cesium.Math.toDegrees(horizontalAngle)
    verticalAngle = Cesium.Math.toDegrees(verticalAngle)
    // 修正范围
    const hStar = horizontalAngle - horizontalRangeDeg / 2
    const hEnd = horizontalAngle + horizontalRangeDeg / 2
    const vStar = verticalAngle - verticalRangeDeg / 2
    const vEnd = verticalAngle - verticalRangeDeg / 2
    return {
      startAngle: hStar,
      endAngle: hEnd,
      vStartAngle: vStar,
      vEndAngle: vEnd,
    }
  }
  private computrRadarScanAngles(
    roll: number,
    pitch: number,
    yaw: number,
    horizontalStart: number,
    horizontalEnd: number,
    verticalStart: number,
    verticalEnd: number
  ): any {
    // 构建初始 4 个边界向量
    const baseDirs = [
      this.anglesToCartesian(horizontalStart, verticalStart),
      this.anglesToCartesian(horizontalStart, verticalEnd),
      this.anglesToCartesian(horizontalEnd, verticalStart),
      this.anglesToCartesian(horizontalEnd, verticalEnd),
    ]

    // 创建姿态矩阵（Cesium 默认 HPR：heading=yaw, pitch=pitch, roll=roll）
    const hpr = Cesium.HeadingPitchRoll.fromDegrees(yaw, pitch, roll)
    const rotationMatrix = Cesium.Matrix3.fromHeadingPitchRoll(hpr)

    // 旋转后获取所有边界向量对应的水平角 / 竖直角
    let minAzimuth = Infinity,
      maxAzimuth = -Infinity
    let minElevation = Infinity,
      maxElevation = -Infinity

    for (const dir of baseDirs) {
      const rotated = Cesium.Matrix3.multiplyByVector(
        rotationMatrix,
        dir,
        new Cesium.Cartesian3()
      )
      const { azimuth, elevation } = this.cartesianToAngles(rotated)

      // 修正角度范围到 [0, 360)
      const fixedAz = (azimuth + 360) % 360

      minAzimuth = Math.min(minAzimuth, fixedAz)
      maxAzimuth = Math.max(maxAzimuth, fixedAz)
      minElevation = Math.min(minElevation, elevation)
      maxElevation = Math.max(maxElevation, elevation)
    }

    return {
      startAngle: minAzimuth,
      endAngle: maxAzimuth,
      vStartAngle: minElevation,
      vEndAngle: maxElevation,
    }
  }
  // 将方向向量转换为 水平角（Azimuth）+ 竖直角（Elevation）
  public cartesianToAngles(cartesian: Cesium.Cartesian3) {
    const magnitude = Cesium.Cartesian3.magnitude(cartesian)
    const unit = Cesium.Cartesian3.normalize(cartesian, new Cesium.Cartesian3())

    const elevation = Cesium.Math.toDegrees(Math.asin(unit.z)) // z / length
    const azimuth = Cesium.Math.toDegrees(Math.atan2(unit.y, unit.x)) // atan2(y, x)

    return { azimuth, elevation }
  }

  // 将水平+竖直角 转换为方向向量（球坐标转笛卡尔）
  public anglesToCartesian(horizontalDeg, verticalDeg) {
    const azimuthRad = Cesium.Math.toRadians(horizontalDeg)
    const elevationRad = Cesium.Math.toRadians(verticalDeg)

    const x = Math.cos(elevationRad) * Math.cos(azimuthRad)
    const y = Math.cos(elevationRad) * Math.sin(azimuthRad)
    const z = Math.sin(elevationRad)

    return new Cesium.Cartesian3(x, y, z)
  }
  /**
   * 渲染飞机飘带效果
   */
  private renderAircraftRibbonEffect(effect: EffectInstance): void {
    const model = this.viewer.entities.getById(effect.config.modelId)
    if (!model || !model.position) {
      console.warn(`Model ${effect.config.modelId} not found for ribbon effect`)
      return
    }

    // 获取飞机翼展（从配置或默认值）
    const wingSpan =
      effect.config.customParams?.wingSpan || this.ribbonConfig.wingSpan

    // 初始化飘带数据
    effect.ribbonData = {
      positions: [],
      maxPoints: this.ribbonConfig.maxPoints,
      lastPosition: undefined,
      lastOrientation: undefined,
      wingSpan: wingSpan,
      rollAngles: [],
    }

    // 创建飘带实体 - 使用多条polyline来模拟翼展宽度
    const ribbonEntity = this.viewer.entities.add({
      polyline: {
        positions: new Cesium.CallbackProperty(() => {
          return this.updateRibbonPositions(effect)
        }, false),
        width: new Cesium.CallbackProperty(() => {
          return this.calculateRibbonWidth(effect)
        }, false),
        material: new Cesium.PolylineGlowMaterialProperty({
          glowPower: 0.3,
          color: effect.config.color || this.ribbonConfig.color,
        }),
      },
    })
    console.log(
      '🚀 ~ EffectManager ~ renderAircraftRibbonEffect ~ ribbonEntity:',
      ribbonEntity
    )

    effect.entity = ribbonEntity
    effect.isActive = true
  }

  /**
   * 更新飘带位置
   */
  private updateRibbonPositions(effect: EffectInstance): Cesium.Cartesian3[] {
    if (!effect.ribbonData) return []

    const model = this.viewer.entities.getById(effect.config.modelId)
    if (!model || !model.position) return effect.ribbonData.positions

    const currentTime = Cesium.JulianDate.now()
    const currentPosition = model.position.getValue(currentTime)
    const currentOrientation = model.orientation?.getValue(currentTime)

    if (!currentPosition) return effect.ribbonData.positions

    // 检查是否需要添加新点
    const shouldAddPoint = this.shouldAddRibbonPoint(
      effect.ribbonData,
      currentPosition,
      currentOrientation
    )

    if (shouldAddPoint) {
      // 计算飘带起始点（飞机尾部）
      const ribbonStartPoint = this.calculateRibbonStartPoint(
        currentPosition,
        currentOrientation
      )

      // 提取翻滚角度
      const rollAngle = this.extractRollAngle(currentOrientation)

      effect.ribbonData.positions.push(ribbonStartPoint)
      effect.ribbonData.rollAngles.push(rollAngle)
      effect.ribbonData.lastPosition = currentPosition.clone()
      effect.ribbonData.lastOrientation = currentOrientation?.clone()

      // 限制点数
      if (effect.ribbonData.positions.length > effect.ribbonData.maxPoints) {
        effect.ribbonData.positions.shift()
        effect.ribbonData.rollAngles.shift()
      }
    }

    return effect.ribbonData.positions
  }

  /**
   * 判断是否应该添加新的飘带点
   */
  private shouldAddRibbonPoint(
    ribbonData: NonNullable<EffectInstance['ribbonData']>,
    currentPosition: Cesium.Cartesian3,
    currentOrientation?: Cesium.Quaternion
  ): boolean {
    if (!ribbonData.lastPosition) return true

    const distance = Cesium.Cartesian3.distance(
      currentPosition,
      ribbonData.lastPosition
    )

    // 基于距离和姿态变化判断
    if (distance > this.ribbonConfig.minDistance) return true

    // 检查姿态变化
    if (currentOrientation && ribbonData.lastOrientation) {
      const dot = Cesium.Quaternion.dot(
        currentOrientation,
        ribbonData.lastOrientation
      )
      const orientationChange = Math.acos(Math.abs(dot)) * 2
      if (orientationChange > Cesium.Math.toRadians(5)) return true
    }

    return false
  }

  /**
   * 计算飘带起始点（飞机尾部位置）
   */
  private calculateRibbonStartPoint(
    position: Cesium.Cartesian3,
    orientation?: Cesium.Quaternion
  ): Cesium.Cartesian3 {
    if (!orientation) return position.clone()

    // 创建变换矩阵
    const transform = Cesium.Transforms.eastNorthUpToFixedFrame(position)
    const rotationMatrix = Cesium.Matrix3.fromQuaternion(orientation)

    // 飞机尾部偏移（相对于飞机本地坐标系）
    const tailOffset = new Cesium.Cartesian3(-20, 0, -2) // 向后20米，向下2米

    // 将偏移转换到世界坐标系
    const worldOffset = Cesium.Matrix3.multiplyByVector(
      rotationMatrix,
      tailOffset,
      new Cesium.Cartesian3()
    )

    // 应用到世界坐标
    const worldPosition = Cesium.Matrix4.multiplyByPoint(
      transform,
      worldOffset,
      new Cesium.Cartesian3()
    )

    return Cesium.Cartesian3.add(
      position,
      worldPosition,
      new Cesium.Cartesian3()
    )
  }

  /**
   * 计算飘带宽度（基于GLB模型实际渲染宽度和翻滚角度）
   */
  private calculateRibbonWidth(effect: EffectInstance): number {
    if (!effect.ribbonData) {
      return this.ribbonConfig.ribbonWidth
    }

    // 获取模型实体
    const entity = this.viewer.entities.getById(effect.modelId)
    if (!entity || !entity.position) {
      return this.ribbonConfig.ribbonWidth
    }

    try {
      const currentTime = this.viewer.clock.currentTime
      const position = entity.position.getValue(currentTime)
      const orientation = entity.orientation?.getValue(currentTime)

      if (!position) {
        return this.ribbonConfig.ribbonWidth
      }

      // 计算模型在屏幕上的像素宽度
      const modelScreenWidth = this.calculateModelScreenWidth(
        entity,
        position,
        orientation
      )

      // 获取翻滚角度影响
      let rollFactor = 1.0
      if (effect.ribbonData.rollAngles.length > 0) {
        const latestRollAngle =
          effect.ribbonData.rollAngles[effect.ribbonData.rollAngles.length - 1]
        rollFactor = Math.abs(Math.cos(latestRollAngle))
      }

      // 飘带宽度 = 模型屏幕宽度 * 翻滚因子 * 调整系数
      const ribbonWidth = modelScreenWidth * rollFactor * 0.8 // 0.8是调整系数，使飘带略窄于模型

      // 调试信息（仅在前几次计算时输出）
      if (Math.random() < 0.01) {
        // 1%的概率输出调试信息
        console.log(`Ribbon width calculation for ${effect.modelId}:`, {
          modelScreenWidth: modelScreenWidth.toFixed(2),
          rollFactor: rollFactor.toFixed(2),
          finalWidth: ribbonWidth.toFixed(2),
          distance:
            Cesium.Cartesian3.distance(
              this.viewer.camera.position,
              position
            ).toFixed(0) + 'm',
        })
      }

      return Math.max(this.ribbonConfig.ribbonWidth, ribbonWidth)
    } catch (error) {
      console.warn('Error calculating ribbon width:', error)
      return this.ribbonConfig.ribbonWidth
    }
  }

  /**
   * 提取四元数中的翻滚角度
   */
  private extractRollAngle(orientation?: Cesium.Quaternion): number {
    if (!orientation) return 0

    // 将四元数转换为欧拉角
    const heading = Cesium.Math.toDegrees(
      Math.atan2(
        2 * (orientation.w * orientation.z + orientation.x * orientation.y),
        1 - 2 * (orientation.y * orientation.y + orientation.z * orientation.z)
      )
    )

    const pitch = Cesium.Math.toDegrees(
      Math.asin(
        2 * (orientation.w * orientation.y - orientation.z * orientation.x)
      )
    )

    const roll = Cesium.Math.toDegrees(
      Math.atan2(
        2 * (orientation.w * orientation.x + orientation.y * orientation.z),
        1 - 2 * (orientation.x * orientation.x + orientation.y * orientation.y)
      )
    )

    return Cesium.Math.toRadians(roll)
  }

  /**
   * 创建增强的飘带起始点（考虑翼展）
   */
  private calculateEnhancedRibbonStartPoint(
    position: Cesium.Cartesian3,
    orientation?: Cesium.Quaternion,
    wingOffset: number = 0
  ): Cesium.Cartesian3 {
    if (!orientation) return position.clone()

    // 创建变换矩阵
    const transform = Cesium.Transforms.eastNorthUpToFixedFrame(position)
    const rotationMatrix = Cesium.Matrix3.fromQuaternion(orientation)

    // 飞机尾部偏移（相对于飞机本地坐标系）
    // 加入翼展偏移来创建飘带的宽度效果
    const tailOffset = new Cesium.Cartesian3(-20, wingOffset, -2)

    // 将偏移转换到世界坐标系
    const worldOffset = Cesium.Matrix3.multiplyByVector(
      rotationMatrix,
      tailOffset,
      new Cesium.Cartesian3()
    )

    // 应用到世界坐标
    const worldPosition = Cesium.Matrix4.multiplyByPoint(
      transform,
      worldOffset,
      new Cesium.Cartesian3()
    )

    return Cesium.Cartesian3.add(
      position,
      worldPosition,
      new Cesium.Cartesian3()
    )
  }

  /**
   * 计算GLB模型在屏幕上的像素宽度
   */
  private calculateModelScreenWidth(
    entity: Cesium.Entity,
    position: Cesium.Cartesian3,
    orientation?: Cesium.Quaternion
  ): number {
    try {
      // 获取模型的缩放和最小像素尺寸
      const model = entity.model
      if (!model) {
        return this.ribbonConfig.ribbonWidth
      }

      const scale = model.scale?.getValue(this.viewer.clock.currentTime) || 1.0
      const minimumPixelSize =
        model.minimumPixelSize?.getValue(this.viewer.clock.currentTime) || 0

      // 计算相机到模型的距离
      const cameraPosition = this.viewer.camera.position
      const distance = Cesium.Cartesian3.distance(cameraPosition, position)

      // 估算模型的实际尺寸（基于翼展数据）
      const wingSpanMeters = this.getEntityWingSpan(entity.id || '')

      // 将世界坐标尺寸转换为屏幕像素尺寸
      const canvas = this.viewer.scene.canvas

      // 获取视角，处理不同类型的frustum
      let fov = Math.PI / 3 // 默认60度视角
      const frustum = this.viewer.camera.frustum
      if ('fov' in frustum && typeof frustum.fov === 'number') {
        fov = frustum.fov
      }

      const pixelsPerMeter =
        canvas.clientHeight / 2 / (distance * Math.tan(fov / 2))

      // 计算屏幕上的翼展像素宽度
      let screenWidth = wingSpanMeters * pixelsPerMeter * scale

      // 应用最小像素尺寸限制
      if (minimumPixelSize > 0) {
        screenWidth = Math.max(screenWidth, minimumPixelSize)
      }

      // 限制最大和最小宽度
      return Math.max(2, Math.min(screenWidth, 200))
    } catch (error) {
      console.warn('Error calculating model screen width:', error)
      return this.ribbonConfig.ribbonWidth
    }
  }

  /**
   * 获取实体的翼展数据
   */
  private getEntityWingSpan(entityId: string): number {
    // 飞机类型翼展数据库
    const aircraftTypes: { [key: string]: number } = {
      f18: 12.3, // F-18 Super Hornet
      f16: 9.8, // F-16 Fighting Falcon
      f22: 13.6, // F-22 Raptor
      f35: 10.7, // F-35 Lightning II
      su27: 14.7, // Su-27 Flanker
      su35: 15.3, // Su-35 Flanker-E
      j20: 13.0, // J-20 Mighty Dragon
      j10: 9.75, // J-10 Vigorous Dragon
      mig29: 11.4, // MiG-29 Fulcrum
      eurofighter: 10.95, // Eurofighter Typhoon
      rafale: 10.8, // Dassault Rafale
      cesium_air: 8.5, // Cesium Air 默认模型
      default: 12.0, // 默认翼展
    }

    // 从entityId中提取飞机类型
    const entityIdLower = entityId.toLowerCase()
    for (const [type, wingspan] of Object.entries(aircraftTypes)) {
      if (entityIdLower.includes(type)) {
        return wingspan
      }
    }

    // 如果没有匹配到特定类型，返回默认值
    return aircraftTypes.default
  }

  /**
   * 销毁特效管理器
   */
  public destroy(): void {
    // 移除渲染事件监听
    if (this.renderUpdateHandler) {
      this.viewer.scene.preRender.removeEventListener(this.renderUpdateHandler)
      this.renderUpdateHandler = null
    }
    this.destroyAllEffects()
    // 销毁所有特效
    // this.effects.forEach(effect => {
    //   this.destroyEffect(effect.id)
    // })
    this.effects.clear()
  }
}
