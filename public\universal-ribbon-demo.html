<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通用飘带系统演示</title>
    <script src="https://cesium.com/downloads/cesiumjs/releases/1.95/Build/Cesium/Cesium.js"></script>
    <link href="https://cesium.com/downloads/cesiumjs/releases/1.95/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <style>
        html, body, #cesiumContainer {
            width: 100%; height: 100%; margin: 0; padding: 0; overflow: hidden;
        }
        
        .control-panel {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(42, 42, 42, 0.9);
            padding: 15px;
            border-radius: 8px;
            color: white;
            font-family: Arial, sans-serif;
            z-index: 1000;
            max-width: 300px;
        }
        
        .control-panel h3 {
            margin: 0 0 15px 0;
            color: #48b;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
        }
        
        button {
            background: #48b;
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 2px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
        }
        
        button:hover {
            background: #369;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.7);
            padding: 5px 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 11px;
        }
        
        .model-list {
            max-height: 200px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        .model-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 11px;
        }
        
        .model-type {
            color: #48b;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="cesiumContainer"></div>
    
    <div class="control-panel">
        <h3>🎀 通用飘带系统</h3>
        
        <div class="control-group">
            <label>全局控制</label>
            <button onclick="toggleGlobalRibbons()">切换全局飘带</button>
            <button onclick="addRibbonToAll()">添加所有飘带</button>
            <button onclick="removeAllRibbons()">移除所有飘带</button>
        </div>
        
        <div class="control-group">
            <label>演示场景</label>
            <button onclick="createMixedDemo()">创建混合演示</button>
            <button onclick="startDynamicDemo()">动态飞行演示</button>
            <button onclick="clearAll()">清空场景</button>
        </div>
        
        <div class="control-group">
            <label>特殊效果</label>
            <button onclick="customizeCarrier()">定制航母飘带</button>
            <button onclick="customizeFighter()">定制战机飘带</button>
            <button onclick="rainbowMode()">彩虹模式</button>
        </div>
        
        <div class="status" id="status">
            状态: 准备就绪
        </div>
        
        <div class="model-list" id="modelList">
            <div style="text-align: center; color: #888;">暂无模型</div>
        </div>
    </div>

    <script type="module">
        // 模拟导入（实际项目中使用真实的导入）
        // import { UniversalRibbonExample } from '../src/examples/universalRibbonExample.js'
        
        // 初始化Cesium
        Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.frpOhwF27cIuEfRqTwS8_O0nhiQlCPG9klt0_AvNCZY'
        
        const viewer = new Cesium.Viewer('cesiumContainer', {
            terrainProvider: Cesium.createWorldTerrain(),
            timeline: true,
            animation: true
        })
        
        // 模拟的通用飘带示例类
        class MockUniversalRibbonExample {
            constructor(viewer) {
                this.viewer = viewer
                this.models = new Map()
                this.ribbonEnabled = true
                this.updateStatus('通用飘带系统已初始化')
            }
            
            async createMixedDemo() {
                this.updateStatus('正在创建混合演示...')
                
                // 创建各种类型的模型
                const modelData = [
                    { id: 'fighter_f18', type: '战斗机', color: Cesium.Color.CYAN, pos: [116.0, 39.0, 8000] },
                    { id: 'bomber_b52', type: '轰炸机', color: Cesium.Color.RED, pos: [116.1, 39.1, 9000] },
                    { id: 'carrier_001', type: '航空母舰', color: Cesium.Color.BLUE, pos: [116.2, 39.0, 0] },
                    { id: 'destroyer_001', type: '驱逐舰', color: Cesium.Color.GREEN, pos: [116.3, 39.1, 0] },
                    { id: 'tank_m1a2', type: '主战坦克', color: Cesium.Color.YELLOW, pos: [116.0, 38.9, 100] },
                    { id: 'truck_supply', type: '补给车', color: Cesium.Color.ORANGE, pos: [116.1, 38.9, 100] }
                ]
                
                for (const model of modelData) {
                    await this.createModel(model)
                }
                
                this.updateModelList()
                this.updateStatus('混合演示创建完成')
            }
            
            async createModel(modelData) {
                const entity = this.viewer.entities.add({
                    id: modelData.id,
                    name: modelData.type,
                    position: Cesium.Cartesian3.fromDegrees(...modelData.pos),
                    point: {
                        pixelSize: 10,
                        color: modelData.color,
                        outlineColor: Cesium.Color.WHITE,
                        outlineWidth: 2
                    },
                    label: {
                        text: modelData.type,
                        font: '12pt sans-serif',
                        pixelOffset: new Cesium.Cartesian2(0, -30),
                        fillColor: Cesium.Color.WHITE,
                        style: Cesium.LabelStyle.FILL_AND_OUTLINE
                    }
                })
                
                this.models.set(modelData.id, {
                    entity: entity,
                    type: modelData.type,
                    hasRibbon: this.ribbonEnabled
                })
                
                if (this.ribbonEnabled) {
                    this.addRibbonToModel(modelData.id)
                }
            }
            
            addRibbonToModel(modelId) {
                const model = this.models.get(modelId)
                if (!model || model.hasRibbon) return
                
                // 模拟飘带效果（使用路径线）
                const entity = model.entity
                const positions = []
                const startPos = entity.position.getValue(Cesium.JulianDate.now())
                
                // 生成飘带路径点
                for (let i = 0; i < 20; i++) {
                    const offset = new Cesium.Cartesian3(
                        (Math.random() - 0.5) * 1000,
                        (Math.random() - 0.5) * 1000,
                        (Math.random() - 0.5) * 500
                    )
                    positions.push(Cesium.Cartesian3.add(startPos, offset, new Cesium.Cartesian3()))
                }
                
                const ribbonEntity = this.viewer.entities.add({
                    id: `${modelId}_ribbon`,
                    polyline: {
                        positions: positions,
                        width: this.getRibbonWidth(model.type),
                        material: this.getRibbonColor(model.type),
                        clampToGround: false
                    }
                })
                
                model.hasRibbon = true
                model.ribbonEntity = ribbonEntity
            }
            
            removeRibbonFromModel(modelId) {
                const model = this.models.get(modelId)
                if (!model || !model.hasRibbon) return
                
                if (model.ribbonEntity) {
                    this.viewer.entities.remove(model.ribbonEntity)
                    model.ribbonEntity = null
                }
                model.hasRibbon = false
            }
            
            getRibbonWidth(modelType) {
                const widthMap = {
                    '战斗机': 5,
                    '轰炸机': 8,
                    '航空母舰': 15,
                    '驱逐舰': 10,
                    '主战坦克': 3,
                    '补给车': 2
                }
                return widthMap[modelType] || 5
            }
            
            getRibbonColor(modelType) {
                const colorMap = {
                    '战斗机': Cesium.Color.CYAN.withAlpha(0.8),
                    '轰炸机': Cesium.Color.RED.withAlpha(0.8),
                    '航空母舰': Cesium.Color.BLUE.withAlpha(0.8),
                    '驱逐舰': Cesium.Color.GREEN.withAlpha(0.8),
                    '主战坦克': Cesium.Color.YELLOW.withAlpha(0.8),
                    '补给车': Cesium.Color.ORANGE.withAlpha(0.8)
                }
                return colorMap[modelType] || Cesium.Color.WHITE.withAlpha(0.8)
            }
            
            toggleGlobalRibbons() {
                this.ribbonEnabled = !this.ribbonEnabled
                
                if (this.ribbonEnabled) {
                    this.addRibbonToAllModels()
                    this.updateStatus('全局飘带已启用')
                } else {
                    this.removeRibbonFromAllModels()
                    this.updateStatus('全局飘带已禁用')
                }
                
                this.updateModelList()
            }
            
            addRibbonToAllModels() {
                for (const [modelId, model] of this.models) {
                    this.addRibbonToModel(modelId)
                }
                this.updateStatus('已为所有模型添加飘带')
                this.updateModelList()
            }
            
            removeRibbonFromAllModels() {
                for (const [modelId, model] of this.models) {
                    this.removeRibbonFromModel(modelId)
                }
                this.updateStatus('已移除所有模型飘带')
                this.updateModelList()
            }
            
            customizeCarrier() {
                const model = this.models.get('carrier_001')
                if (!model) return
                
                this.removeRibbonFromModel('carrier_001')
                
                // 创建特殊的金色飘带
                setTimeout(() => {
                    const entity = model.entity
                    const startPos = entity.position.getValue(Cesium.JulianDate.now())
                    const positions = []
                    
                    for (let i = 0; i < 50; i++) {
                        const offset = new Cesium.Cartesian3(
                            Math.sin(i * 0.2) * 2000,
                            Math.cos(i * 0.2) * 2000,
                            (Math.random() - 0.5) * 200
                        )
                        positions.push(Cesium.Cartesian3.add(startPos, offset, new Cesium.Cartesian3()))
                    }
                    
                    const ribbonEntity = this.viewer.entities.add({
                        id: 'carrier_001_ribbon',
                        polyline: {
                            positions: positions,
                            width: 20,
                            material: Cesium.Color.GOLD.withAlpha(0.9)
                        }
                    })
                    
                    model.hasRibbon = true
                    model.ribbonEntity = ribbonEntity
                    this.updateStatus('航母飘带已定制为金色特效')
                    this.updateModelList()
                }, 100)
            }
            
            customizeFighter() {
                const model = this.models.get('fighter_f18')
                if (!model) return
                
                this.removeRibbonFromModel('fighter_f18')
                
                setTimeout(() => {
                    const entity = model.entity
                    const startPos = entity.position.getValue(Cesium.JulianDate.now())
                    const positions = []
                    
                    for (let i = 0; i < 30; i++) {
                        const offset = new Cesium.Cartesian3(
                            (Math.random() - 0.5) * 500,
                            (Math.random() - 0.5) * 500,
                            Math.sin(i * 0.5) * 1000
                        )
                        positions.push(Cesium.Cartesian3.add(startPos, offset, new Cesium.Cartesian3()))
                    }
                    
                    const ribbonEntity = this.viewer.entities.add({
                        id: 'fighter_f18_ribbon',
                        polyline: {
                            positions: positions,
                            width: 8,
                            material: new Cesium.PolylineGlowMaterialProperty({
                                glowPower: 0.2,
                                color: Cesium.Color.CYAN
                            })
                        }
                    })
                    
                    model.hasRibbon = true
                    model.ribbonEntity = ribbonEntity
                    this.updateStatus('战机飘带已定制为发光特效')
                    this.updateModelList()
                }, 100)
            }
            
            rainbowMode() {
                const colors = [
                    Cesium.Color.RED,
                    Cesium.Color.ORANGE,
                    Cesium.Color.YELLOW,
                    Cesium.Color.GREEN,
                    Cesium.Color.BLUE,
                    Cesium.Color.PURPLE
                ]
                
                let colorIndex = 0
                for (const [modelId, model] of this.models) {
                    this.removeRibbonFromModel(modelId)
                    
                    setTimeout(() => {
                        const entity = model.entity
                        const startPos = entity.position.getValue(Cesium.JulianDate.now())
                        const positions = []
                        
                        for (let i = 0; i < 25; i++) {
                            const offset = new Cesium.Cartesian3(
                                (Math.random() - 0.5) * 1500,
                                (Math.random() - 0.5) * 1500,
                                (Math.random() - 0.5) * 800
                            )
                            positions.push(Cesium.Cartesian3.add(startPos, offset, new Cesium.Cartesian3()))
                        }
                        
                        const ribbonEntity = this.viewer.entities.add({
                            id: `${modelId}_ribbon`,
                            polyline: {
                                positions: positions,
                                width: 10,
                                material: colors[colorIndex % colors.length].withAlpha(0.8)
                            }
                        })
                        
                        model.hasRibbon = true
                        model.ribbonEntity = ribbonEntity
                        colorIndex++
                    }, colorIndex * 200)
                }
                
                this.updateStatus('彩虹模式已激活')
                setTimeout(() => this.updateModelList(), 2000)
            }
            
            clearAll() {
                this.viewer.entities.removeAll()
                this.models.clear()
                this.updateStatus('场景已清空')
                this.updateModelList()
            }
            
            updateStatus(message) {
                document.getElementById('status').textContent = `状态: ${message}`
            }
            
            updateModelList() {
                const listElement = document.getElementById('modelList')
                
                if (this.models.size === 0) {
                    listElement.innerHTML = '<div style="text-align: center; color: #888;">暂无模型</div>'
                    return
                }
                
                let html = ''
                for (const [modelId, model] of this.models) {
                    const ribbonStatus = model.hasRibbon ? '✅' : '❌'
                    html += `
                        <div class="model-item">
                            <span class="model-type">${model.type}</span>
                            <span>${ribbonStatus}</span>
                        </div>
                    `
                }
                
                listElement.innerHTML = html
            }
        }
        
        // 初始化示例
        const example = new MockUniversalRibbonExample(viewer)
        
        // 全局函数
        window.toggleGlobalRibbons = () => example.toggleGlobalRibbons()
        window.addRibbonToAll = () => example.addRibbonToAllModels()
        window.removeAllRibbons = () => example.removeRibbonFromAllModels()
        window.createMixedDemo = () => example.createMixedDemo()
        window.startDynamicDemo = () => example.updateStatus('动态演示功能开发中...')
        window.clearAll = () => example.clearAll()
        window.customizeCarrier = () => example.customizeCarrier()
        window.customizeFighter = () => example.customizeFighter()
        window.rainbowMode = () => example.rainbowMode()
        
        // 设置初始视角
        viewer.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(116.1, 39.05, 50000),
            orientation: {
                heading: 0.0,
                pitch: -0.5,
                roll: 0.0
            }
        })
        
        // 欢迎消息
        setTimeout(() => {
            example.updateStatus('欢迎使用通用飘带系统演示！')
        }, 1000)
    </script>
</body>
</html>
