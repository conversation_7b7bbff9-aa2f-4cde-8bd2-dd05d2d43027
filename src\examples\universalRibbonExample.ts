/**
 * 通用飘带效果使用示例
 * 展示如何为所有类型的模型添加飘带效果
 */

import { EffectManager } from '../utils/effectManager'
import { ModelManager } from '../utils/modelControler'

export class UniversalRibbonExample {
  private viewer: Cesium.Viewer
  private effectManager: EffectManager
  private modelManager: ModelManager

  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer
    
    // 初始化特效管理器
    this.effectManager = new EffectManager(
      viewer,
      'ws://localhost:8080/effects' // 替换为实际的WebSocket地址
    )
    
    // 初始化模型管理器，启用默认飘带效果
    this.modelManager = new ModelManager(viewer, this.effectManager, {
      enableRibbonByDefault: true, // 默认为所有模型添加飘带
      maxTrailPoints: 50
    })
  }

  /**
   * 创建混合演示场景（包含各种类型的模型）
   */
  public async createMixedDemo(): Promise<void> {
    console.log('Creating mixed demo with various model types...')

    // 创建飞机模型
    await this.createAircraftModels()
    
    // 创建舰船模型
    await this.createShipModels()
    
    // 创建地面车辆模型
    await this.createVehicleModels()
    
    // 创建其他类型模型
    await this.createOtherModels()

    console.log('Mixed demo created successfully!')
  }

  /**
   * 创建飞机模型
   */
  private async createAircraftModels(): Promise<void> {
    const aircraftData = [
      {
        na: 'fighter_f18_001',
        ic: 'f18_fighter',
        lo: 116.0,
        la: 39.0,
        al: 8000,
        si: 'blue',
        ya: 0,
        pi: 0,
        ro: 0,
        type: 'aircraft'
      },
      {
        na: 'bomber_b52_001',
        ic: 'b52_bomber',
        lo: 116.1,
        la: 39.1,
        al: 9000,
        si: 'red',
        ya: 45,
        pi: 0,
        ro: 0,
        type: 'aircraft'
      }
    ]

    for (const aircraft of aircraftData) {
      await this.modelManager.createNewModel(aircraft)
    }
  }

  /**
   * 创建舰船模型
   */
  private async createShipModels(): Promise<void> {
    const shipData = [
      {
        na: 'carrier_001',
        ic: 'aircraft_carrier',
        lo: 116.2,
        la: 39.0,
        al: 0,
        si: 'blue',
        ya: 90,
        pi: 0,
        ro: 0,
        type: 'ship'
      },
      {
        na: 'destroyer_001',
        ic: 'destroyer_ship',
        lo: 116.3,
        la: 39.1,
        al: 0,
        si: 'red',
        ya: 180,
        pi: 0,
        ro: 0,
        type: 'ship'
      }
    ]

    for (const ship of shipData) {
      await this.modelManager.createNewModel(ship)
    }
  }

  /**
   * 创建地面车辆模型
   */
  private async createVehicleModels(): Promise<void> {
    const vehicleData = [
      {
        na: 'tank_m1a2_001',
        ic: 'tank_m1a2',
        lo: 116.0,
        la: 38.9,
        al: 100,
        si: 'blue',
        ya: 270,
        pi: 0,
        ro: 0,
        type: 'vehicle'
      },
      {
        na: 'truck_supply_001',
        ic: 'supply_truck',
        lo: 116.1,
        la: 38.9,
        al: 100,
        si: 'red',
        ya: 0,
        pi: 0,
        ro: 0,
        type: 'vehicle'
      }
    ]

    for (const vehicle of vehicleData) {
      await this.modelManager.createNewModel(vehicle)
    }
  }

  /**
   * 创建其他类型模型
   */
  private async createOtherModels(): Promise<void> {
    const otherData = [
      {
        na: 'satellite_001',
        ic: 'communication_satellite',
        lo: 116.05,
        la: 39.05,
        al: 35000000, // 地球同步轨道高度
        si: 'blue',
        ya: 0,
        pi: 0,
        ro: 0,
        type: 'satellite'
      },
      {
        na: 'missile_001',
        ic: 'ballistic_missile',
        lo: 116.15,
        la: 39.15,
        al: 15000,
        si: 'red',
        ya: 45,
        pi: 30,
        ro: 0,
        type: 'missile'
      }
    ]

    for (const model of otherData) {
      await this.modelManager.createNewModel(model)
    }
  }

  /**
   * 演示飘带效果的动态控制
   */
  public demonstrateRibbonControl(): void {
    console.log('Demonstrating ribbon control...')

    // 禁用默认飘带效果
    setTimeout(() => {
      console.log('Disabling ribbons for all models...')
      this.modelManager.removeRibbonFromAllModels()
      this.modelManager.setRibbonByDefault(false)
    }, 5000)

    // 重新启用飘带效果
    setTimeout(() => {
      console.log('Re-enabling ribbons for all models...')
      this.modelManager.setRibbonByDefault(true)
      this.modelManager.addRibbonToAllModels()
    }, 10000)

    // 自定义特定模型的飘带配置
    setTimeout(() => {
      console.log('Customizing ribbon for specific models...')
      
      // 为航母设置特殊的飘带效果
      this.modelManager.updateRibbonConfig('carrier_001', {
        color: Cesium.Color.GOLD.withAlpha(0.9),
        wingSpan: 80.0,
        maxPoints: 100,
        minDistance: 50.0
      })

      // 为战斗机设置高灵敏度飘带
      this.modelManager.updateRibbonConfig('fighter_f18_001', {
        color: Cesium.Color.CYAN.withAlpha(0.9),
        rollSensitivity: 2.0,
        maxPoints: 80
      })
    }, 15000)
  }

  /**
   * 创建动态飞行演示
   */
  public createDynamicFlightDemo(): void {
    // 为飞机创建动态飞行路径
    const startTime = Cesium.JulianDate.now()
    const stopTime = Cesium.JulianDate.addSeconds(startTime, 120, new Cesium.JulianDate())

    // 创建飞行路径
    const positions = []
    for (let i = 0; i <= 120; i += 2) {
      const lon = 116.0 + Math.sin(i * 0.1) * 0.1
      const lat = 39.0 + Math.cos(i * 0.1) * 0.1
      const alt = 8000 + Math.sin(i * 0.05) * 2000
      
      positions.push(Cesium.Cartesian3.fromDegrees(lon, lat, alt))
    }

    const property = new Cesium.SampledPositionProperty()
    for (let i = 0; i < positions.length; i++) {
      const time = Cesium.JulianDate.addSeconds(startTime, i * 2, new Cesium.JulianDate())
      property.addSample(time, positions[i])
    }

    // 更新飞机位置
    const aircraft = this.viewer.entities.getById('fighter_f18_001')
    if (aircraft) {
      aircraft.position = property
      aircraft.orientation = new Cesium.VelocityOrientationProperty(property)
    }

    // 设置时间轴
    this.viewer.clock.startTime = startTime
    this.viewer.clock.stopTime = stopTime
    this.viewer.clock.currentTime = startTime
    this.viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP
    this.viewer.clock.multiplier = 1

    console.log('Dynamic flight demo started!')
  }

  /**
   * 清理所有演示内容
   */
  public cleanup(): void {
    this.viewer.entities.removeAll()
    this.modelManager.removeRibbonFromAllModels()
    console.log('Demo cleanup completed!')
  }

  /**
   * 获取模型管理器实例（用于外部控制）
   */
  public getModelManager(): ModelManager {
    return this.modelManager
  }

  /**
   * 获取特效管理器实例（用于外部控制）
   */
  public getEffectManager(): EffectManager {
    return this.effectManager
  }
}
