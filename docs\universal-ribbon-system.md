# 通用飘带系统使用指南

## 概述

通用飘带系统是对原有飞机飘带效果的扩展，现在支持为**所有类型的模型**默认添加飘带效果。系统会根据模型类型自动调整飘带参数，提供最佳的视觉效果。

## 新特性

### 🎯 默认飘带效果
- **自动识别**: 系统自动识别模型类型（飞机、舰船、车辆等）
- **智能参数**: 根据模型类型自动调整飘带参数
- **一键开关**: 可以全局启用或禁用默认飘带效果

### 🔧 智能参数调整
- **飞机模型**: 使用真实翼展数据，高灵敏度姿态响应
- **舰船模型**: 较大的飘带宽度，低灵敏度滚转响应
- **地面车辆**: 较小的飘带参数，适合地面移动
- **其他模型**: 通用参数，适用于未知类型

### 🎨 灵活配置
- **批量操作**: 支持为所有现有模型添加或移除飘带
- **个性化定制**: 可为特定模型自定义飘带参数
- **实时调整**: 支持运行时动态修改配置

## 快速开始

### 1. 基础使用

```typescript
import { EffectManager } from '../utils/effectManager'
import { ModelManager } from '../utils/modelControler'

// 初始化特效管理器
const effectManager = new EffectManager(viewer, wsUrl)

// 初始化模型管理器，启用默认飘带
const modelManager = new ModelManager(viewer, effectManager, {
  enableRibbonByDefault: true, // 默认为所有模型添加飘带
  maxTrailPoints: 50
})

// 创建任意类型的模型，自动添加飘带效果
await modelManager.createNewModel({
  na: 'model_001',
  ic: 'any_model_type',
  lo: 116.0,
  la: 39.0,
  al: 5000,
  si: 'blue',
  type: 'aircraft' // 或 'ship', 'vehicle', 'satellite' 等
})
```

### 2. 使用通用飘带示例类

```typescript
import { UniversalRibbonExample } from '../examples/universalRibbonExample'

// 初始化示例
const example = new UniversalRibbonExample(viewer)

// 创建混合演示（包含各种类型的模型）
await example.createMixedDemo()

// 演示动态控制
example.demonstrateRibbonControl()

// 创建动态飞行演示
example.createDynamicFlightDemo()
```

## 配置参数

### ModelManager 配置

```typescript
const config = {
  enableRibbonByDefault: true,    // 是否默认添加飘带
  maxTrailPoints: 50,             // 飘带最大点数
  trailWidth: 3,                  // 飘带线宽
  trailColor: Cesium.Color.CYAN   // 默认飘带颜色
}
```

### 模型类型参数映射

| 模型类型 | 翼展/宽度 | 最小距离 | 滚转灵敏度 | 说明 |
|----------|-----------|----------|------------|------|
| 飞机 | 实际翼展 | 15.0m | 1.0 | 使用真实飞机数据 |
| 舰船 | 50.0m | 25.0m | 0.3 | 适合大型舰船 |
| 车辆 | 8.0m | 5.0m | 0.5 | 适合地面车辆 |
| 默认 | 12.0m | 10.0m | 0.8 | 未知类型模型 |

## API 参考

### ModelManager 新增方法

#### setRibbonByDefault(enabled: boolean)
启用或禁用默认飘带效果

```typescript
// 禁用默认飘带
modelManager.setRibbonByDefault(false)

// 启用默认飘带
modelManager.setRibbonByDefault(true)
```

#### addRibbonToAllModels()
为所有现有模型添加飘带效果

```typescript
modelManager.addRibbonToAllModels()
```

#### removeRibbonFromAllModels()
移除所有模型的飘带效果

```typescript
modelManager.removeRibbonFromAllModels()
```

#### updateRibbonConfig(modelId, config)
更新特定模型的飘带配置

```typescript
modelManager.updateRibbonConfig('model_001', {
  color: Cesium.Color.GOLD.withAlpha(0.9),
  wingSpan: 20.0,
  maxPoints: 100,
  minDistance: 20.0,
  rollSensitivity: 1.5
})
```

## 高级功能

### 1. 自定义模型类型识别

系统通过以下方式识别模型类型：
- `modelType` 参数
- `modelIcon` 中的关键词
- `modelId` 中的关键词

支持的关键词：
- **飞机**: aircraft, plane, fighter, bomber, f18, f16, su27 等
- **舰船**: ship, boat, carrier, destroyer, frigate, submarine
- **车辆**: vehicle, tank, truck, car

### 2. 动态场景演示

```typescript
// 创建包含多种模型类型的场景
const models = [
  { type: 'aircraft', icon: 'f18_fighter' },
  { type: 'ship', icon: 'aircraft_carrier' },
  { type: 'vehicle', icon: 'tank_m1a2' },
  { type: 'satellite', icon: 'communication_sat' }
]

for (const model of models) {
  await modelManager.createNewModel({
    na: `${model.type}_001`,
    ic: model.icon,
    // ... 其他参数
  })
}
```

### 3. 实时控制演示

```typescript
// 5秒后禁用所有飘带
setTimeout(() => {
  modelManager.removeRibbonFromAllModels()
}, 5000)

// 10秒后重新启用
setTimeout(() => {
  modelManager.addRibbonToAllModels()
}, 10000)

// 15秒后自定义特定模型
setTimeout(() => {
  modelManager.updateRibbonConfig('special_model', {
    color: Cesium.Color.RAINBOW,
    wingSpan: 100.0
  })
}, 15000)
```

## 性能优化

### 1. 批量操作
- 使用 `addRibbonToAllModels()` 而不是逐个添加
- 使用 `removeRibbonFromAllModels()` 进行批量清理

### 2. 参数调优
```typescript
// 针对不同场景的优化配置
const performanceConfig = {
  // 高性能场景
  highPerformance: {
    maxTrailPoints: 20,
    minDistance: 20.0
  },
  
  // 高质量场景
  highQuality: {
    maxTrailPoints: 100,
    minDistance: 5.0
  }
}
```

### 3. 内存管理
- 系统自动清理销毁的模型飘带
- 支持对象池减少GC压力
- 智能LOD控制

## 故障排除

### 常见问题

1. **飘带不显示**
   - 检查 `enableRibbonByDefault` 是否为 true
   - 确认模型实体存在且有position属性
   - 验证EffectManager是否正确初始化

2. **飘带参数不合适**
   - 使用 `updateRibbonConfig()` 自定义参数
   - 检查模型类型识别是否正确
   - 调整 `minDistance` 和 `rollSensitivity`

3. **性能问题**
   - 减少 `maxTrailPoints` 数量
   - 增加 `minDistance` 阈值
   - 使用 `setRibbonByDefault(false)` 禁用不需要的飘带

### 调试方法

```typescript
// 检查当前配置
console.log('Ribbon enabled:', modelManager.config.enableRibbonByDefault)

// 监听模型创建
modelManager.on('modelCreated', (modelId) => {
  console.log(`Model created: ${modelId}`)
})

// 检查飘带状态
const effect = effectManager.effects.get(`${modelId}_7`)
console.log('Ribbon effect:', effect)
```

## 示例项目

完整的示例代码请参考：
- `src/examples/universalRibbonExample.ts` - 通用飘带示例
- `src/utils/modelControler.ts` - 核心实现
- `src/utils/effectManager.ts` - 特效管理器

## 许可证

本项目遵循 MIT 许可证。
