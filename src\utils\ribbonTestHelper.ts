/**
 * 飘带测试辅助工具
 * 用于快速测试和验证飘带功能
 */

import { EffectManager } from './effectManager'
import { ModelManager } from './modelControler'

export class RibbonTestHelper {
  private viewer: Cesium.Viewer
  private effectManager: EffectManager
  private modelManager: ModelManager

  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer
    
    // 初始化特效管理器
    this.effectManager = new EffectManager(
      viewer,
      'ws://localhost:8080/effects' // 替换为实际的WebSocket地址
    )
    
    // 初始化模型管理器，启用默认飘带
    this.modelManager = new ModelManager(viewer, this.effectManager, {
      enableRibbonByDefault: true,
      maxTrailPoints: 50
    })
  }

  /**
   * 快速测试 - 创建一个带飘带的测试模型
   */
  public async quickTest(): Promise<void> {
    console.log('🧪 开始快速飘带测试...')

    try {
      // 清理现有测试模型
      this.cleanupTestModels()

      // 创建测试模型
      const testModel = {
        na: 'ribbon_test_001',
        ic: 'f18_test',
        lo: 116.0,
        la: 39.0,
        al: 8000,
        si: 'blue',
        ya: 0,
        pi: 0,
        ro: 0,
        type: 'aircraft'
      }

      await this.modelManager.createNewModel(testModel)
      console.log('✅ 测试模型已创建')

      // 验证模型是否存在
      const entity = this.viewer.entities.getById(testModel.na)
      if (!entity) {
        throw new Error('测试模型创建失败')
      }

      // 验证飘带效果是否创建
      setTimeout(() => {
        const effect = this.effectManager.effects?.get(`${testModel.na}_7`)
        if (effect) {
          console.log('✅ 飘带效果已创建')
          console.log('📊 飘带效果详情:', {
            id: effect.id,
            type: effect.type,
            modelId: effect.config.modelId,
            color: effect.config.color,
            isActive: effect.isActive
          })
        } else {
          console.error('❌ 飘带效果创建失败')
          this.diagnoseIssue(testModel.na)
        }
      }, 500)

      // 设置相机视角
      this.viewer.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(116.0, 39.0, 15000),
        orientation: {
          heading: 0.0,
          pitch: -0.5,
          roll: 0.0
        }
      })

      // 创建简单的移动动画来测试飘带
      this.createTestAnimation(testModel.na)

    } catch (error) {
      console.error('❌ 快速测试失败:', error)
    }
  }

  /**
   * 创建测试动画
   */
  private createTestAnimation(modelId: string): void {
    const entity = this.viewer.entities.getById(modelId)
    if (!entity) return

    console.log('🎬 创建测试动画...')

    const startTime = Cesium.JulianDate.now()
    const stopTime = Cesium.JulianDate.addSeconds(startTime, 60, new Cesium.JulianDate())

    // 创建移动路径
    const positions = []
    const times = []
    
    for (let i = 0; i <= 60; i++) {
      times.push(Cesium.JulianDate.addSeconds(startTime, i, new Cesium.JulianDate()))
      positions.push(Cesium.Cartesian3.fromDegrees(
        116.0 + Math.sin(i * 0.1) * 0.02, // 经度变化
        39.0 + Math.cos(i * 0.1) * 0.02,  // 纬度变化
        8000 + Math.sin(i * 0.2) * 2000   // 高度变化
      ))
    }

    // 设置位置和方向
    entity.position = new Cesium.SampledPositionProperty()
    entity.position.addSamples(times, positions)
    entity.orientation = new Cesium.VelocityOrientationProperty(entity.position)

    // 设置时钟
    this.viewer.clock.startTime = startTime
    this.viewer.clock.stopTime = stopTime
    this.viewer.clock.currentTime = startTime
    this.viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP
    this.viewer.clock.multiplier = 2 // 2倍速
    this.viewer.clock.shouldAnimate = true

    console.log('✅ 测试动画已启动')
  }

  /**
   * 诊断问题
   */
  private diagnoseIssue(modelId: string): void {
    console.log('🔍 诊断飘带问题...')

    // 检查模型
    const entity = this.viewer.entities.getById(modelId)
    if (!entity) {
      console.error('❌ 模型不存在')
      return
    }

    // 检查位置
    try {
      const position = entity.position?.getValue(Cesium.JulianDate.now())
      if (position) {
        console.log('✅ 模型位置正常')
      } else {
        console.error('❌ 模型位置无效')
      }
    } catch (error) {
      console.error('❌ 获取模型位置失败:', error)
    }

    // 检查配置
    const config = this.modelManager['config']
    console.log('📋 模型管理器配置:', {
      enableRibbonByDefault: config.enableRibbonByDefault,
      maxTrailPoints: config.maxTrailPoints
    })

    // 检查特效管理器
    console.log('📋 特效管理器状态:', {
      effectsCount: this.effectManager.effects?.size || 0,
      wsState: this.effectManager.ws?.readyState
    })
  }

  /**
   * 手动添加飘带
   */
  public manualAddRibbon(modelId: string): void {
    console.log(`🎨 手动为模型 ${modelId} 添加飘带...`)

    try {
      this.modelManager.addRibbonEffect(modelId, 'blue', 'aircraft', 'test_aircraft')
      console.log('✅ 手动添加飘带成功')
    } catch (error) {
      console.error('❌ 手动添加飘带失败:', error)
    }
  }

  /**
   * 批量测试
   */
  public async batchTest(): Promise<void> {
    console.log('🧪 开始批量飘带测试...')

    const testModels = [
      { na: 'test_aircraft_001', type: 'aircraft', ic: 'f18', lo: 116.0, la: 39.0, al: 8000 },
      { na: 'test_ship_001', type: 'ship', ic: 'destroyer', lo: 116.01, la: 39.01, al: 0 },
      { na: 'test_vehicle_001', type: 'vehicle', ic: 'tank', lo: 116.02, la: 39.02, al: 100 }
    ]

    for (const model of testModels) {
      try {
        await this.modelManager.createNewModel({
          ...model,
          si: 'blue',
          ya: 0,
          pi: 0,
          ro: 0
        })
        console.log(`✅ 创建模型: ${model.na}`)
      } catch (error) {
        console.error(`❌ 创建模型失败: ${model.na}`, error)
      }
    }

    // 检查结果
    setTimeout(() => {
      const effectCount = Array.from(this.effectManager.effects?.values() || [])
        .filter(effect => effect.type === 7).length
      console.log(`📊 批量测试结果: 创建了 ${effectCount} 个飘带效果`)
    }, 1000)
  }

  /**
   * 清理测试模型
   */
  public cleanupTestModels(): void {
    console.log('🧹 清理测试模型...')

    const testIds = [
      'ribbon_test_001',
      'test_aircraft_001',
      'test_ship_001',
      'test_vehicle_001'
    ]

    testIds.forEach(id => {
      const entity = this.viewer.entities.getById(id)
      if (entity) {
        this.viewer.entities.remove(entity)
        console.log(`🗑️ 已移除: ${id}`)
      }
    })

    // 清理特效
    this.effectManager.destroyAllEffects()
    console.log('✅ 清理完成')
  }

  /**
   * 获取状态报告
   */
  public getStatusReport(): object {
    const entities = this.viewer.entities.values
    const effects = Array.from(this.effectManager.effects?.values() || [])
    const ribbonEffects = effects.filter(effect => effect.type === 7)

    return {
      timestamp: new Date().toISOString(),
      entities: {
        total: entities.length,
        withModels: entities.filter(e => e.model).length,
        withPosition: entities.filter(e => e.position).length
      },
      effects: {
        total: effects.length,
        ribbons: ribbonEffects.length,
        active: ribbonEffects.filter(e => e.isActive).length
      },
      config: {
        enableRibbonByDefault: this.modelManager['config'].enableRibbonByDefault,
        maxTrailPoints: this.modelManager['config'].maxTrailPoints
      },
      websocket: {
        state: this.effectManager.ws?.readyState,
        connected: this.effectManager.ws?.readyState === WebSocket.OPEN
      }
    }
  }
}

// 导出便捷函数
export function quickTestRibbons(viewer: Cesium.Viewer): RibbonTestHelper {
  const helper = new RibbonTestHelper(viewer)
  helper.quickTest()
  return helper
}

export function batchTestRibbons(viewer: Cesium.Viewer): RibbonTestHelper {
  const helper = new RibbonTestHelper(viewer)
  helper.batchTest()
  return helper
}
