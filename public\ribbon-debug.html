<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飘带效果调试工具</title>
    <script src="https://cesium.com/downloads/cesiumjs/releases/1.95/Build/Cesium/Cesium.js"></script>
    <link href="https://cesium.com/downloads/cesiumjs/releases/1.95/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <style>
        html, body, #cesiumContainer {
            width: 100%; height: 100%; margin: 0; padding: 0; overflow: hidden;
        }
        
        .debug-panel {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(42, 42, 42, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .debug-panel h3 {
            margin: 0 0 10px 0;
            color: #48CAE4;
        }
        
        .debug-panel button {
            background: #0077BE;
            color: white;
            border: none;
            padding: 8px 12px;
            margin: 5px 5px 5px 0;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
        }
        
        .debug-panel button:hover {
            background: #005A8B;
        }
        
        .log-output {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-size: 10px;
            line-height: 1.4;
        }
        
        .log-success { color: #4CAF50; }
        .log-warning { color: #FF9800; }
        .log-error { color: #F44336; }
        .log-info { color: #2196F3; }
    </style>
</head>
<body>
    <div id="cesiumContainer"></div>
    
    <div class="debug-panel">
        <h3>🔍 飘带效果调试工具</h3>
        
        <div>
            <button onclick="runDiagnostic()">🔍 运行诊断</button>
            <button onclick="createTestModel()">🧪 创建测试模型</button>
            <button onclick="quickFix()">🔧 快速修复</button>
            <button onclick="clearLog()">🗑️ 清空日志</button>
        </div>
        
        <div>
            <button onclick="enableRibbons()">✅ 启用飘带</button>
            <button onclick="disableRibbons()">❌ 禁用飘带</button>
            <button onclick="addRibbonsToAll()">➕ 全部添加</button>
            <button onclick="removeAllRibbons()">➖ 全部移除</button>
        </div>
        
        <div class="log-output" id="logOutput">
            <div class="log-info">📋 点击按钮开始调试...</div>
        </div>
    </div>

    <script>
        // 初始化 Cesium
        Cesium.Ion.defaultAccessToken = 'your_cesium_token_here'; // 替换为你的token
        
        const viewer = new Cesium.Viewer('cesiumContainer', {
            terrainProvider: Cesium.createWorldTerrain(),
            timeline: false,
            animation: false,
            homeButton: false,
            sceneModePicker: false,
            baseLayerPicker: false,
            navigationHelpButton: false,
            fullscreenButton: false,
            vrButton: false
        });

        // 简化的特效管理器
        class SimpleEffectManager {
            constructor(viewer) {
                this.viewer = viewer;
                this.effects = new Map();
                this.ribbonConfig = {
                    maxPoints: 50,
                    wingSpan: 30.0,
                    ribbonWidth: 2.0,
                    color: Cesium.Color.CYAN.withAlpha(0.8),
                    minDistance: 10.0,
                    fadeAlpha: 0.1,
                    rollSensitivity: 1.0
                };
            }

            createEffect(config, modelId, type) {
                const effectId = `${modelId}_${type}`;
                
                if (type === 7) { // 飘带效果
                    this.createRibbonEffect(effectId, config);
                }
                
                return effectId;
            }

            createRibbonEffect(effectId, config) {
                const model = this.viewer.entities.getById(config.modelId);
                if (!model || !model.position) {
                    log(`❌ 模型 ${config.modelId} 不存在或没有位置`, 'error');
                    return;
                }

                const effect = {
                    id: effectId,
                    type: 7,
                    config: config,
                    ribbonData: {
                        positions: [],
                        maxPoints: config.customParams?.maxPoints || 50,
                        lastPosition: undefined,
                        wingSpan: config.customParams?.wingSpan || 30.0
                    },
                    isActive: true
                };

                // 创建飘带实体
                const ribbonEntity = this.viewer.entities.add({
                    polyline: {
                        positions: new Cesium.CallbackProperty(() => {
                            return this.updateRibbonPositions(effect);
                        }, false),
                        width: config.customParams?.wingSpan / 5 || 6,
                        material: new Cesium.PolylineGlowMaterialProperty({
                            glowPower: 0.3,
                            color: config.color || Cesium.Color.CYAN.withAlpha(0.8)
                        })
                    }
                });

                effect.entity = ribbonEntity;
                this.effects.set(effectId, effect);
                
                log(`✅ 飘带效果已创建: ${effectId}`, 'success');
            }

            updateRibbonPositions(effect) {
                if (!effect.ribbonData) return [];

                const model = this.viewer.entities.getById(effect.config.modelId);
                if (!model || !model.position) return effect.ribbonData.positions;

                const currentTime = Cesium.JulianDate.now();
                const currentPosition = model.position.getValue(currentTime);
                
                if (!currentPosition) return effect.ribbonData.positions;

                // 检查是否需要添加新点
                if (!effect.ribbonData.lastPosition || 
                    Cesium.Cartesian3.distance(currentPosition, effect.ribbonData.lastPosition) > 10.0) {
                    
                    effect.ribbonData.positions.push(currentPosition.clone());
                    effect.ribbonData.lastPosition = currentPosition.clone();

                    // 限制点数
                    if (effect.ribbonData.positions.length > effect.ribbonData.maxPoints) {
                        effect.ribbonData.positions.shift();
                    }
                }

                return effect.ribbonData.positions;
            }

            destroyEffect(effectId) {
                const effect = this.effects.get(effectId);
                if (effect && effect.entity) {
                    this.viewer.entities.remove(effect.entity);
                    this.effects.delete(effectId);
                    log(`🗑️ 特效已删除: ${effectId}`, 'info');
                }
            }

            destroyAllEffects() {
                this.effects.forEach((effect, id) => {
                    this.destroyEffect(id);
                });
            }
        }

        // 简化的模型管理器
        class SimpleModelManager {
            constructor(viewer, effectManager) {
                this.viewer = viewer;
                this.effectManager = effectManager;
                this.config = {
                    enableRibbonByDefault: true,
                    maxTrailPoints: 50
                };
            }

            async createTestModel(id, position, options = {}) {
                const entity = this.viewer.entities.add({
                    id: id,
                    name: options.name || '测试模型',
                    position: position,
                    orientation: options.orientation || Cesium.Transforms.headingPitchRollQuaternion(
                        position,
                        new Cesium.HeadingPitchRoll(0, 0, 0)
                    ),
                    point: {
                        pixelSize: 15,
                        color: options.color || Cesium.Color.YELLOW,
                        outlineColor: Cesium.Color.BLACK,
                        outlineWidth: 2
                    },
                    label: {
                        text: options.name || '测试模型',
                        font: '14pt sans-serif',
                        pixelOffset: new Cesium.Cartesian2(0, -40),
                        fillColor: Cesium.Color.WHITE,
                        style: Cesium.LabelStyle.FILL_AND_OUTLINE
                    }
                });

                // 如果启用默认飘带，自动添加
                if (this.config.enableRibbonByDefault) {
                    this.addRibbonEffect(id, options.side || 'blue');
                }

                return entity;
            }

            addRibbonEffect(modelId, side = 'blue') {
                const ribbonColor = side === 'red' 
                    ? Cesium.Color.RED.withAlpha(0.8)
                    : Cesium.Color.BLUE.withAlpha(0.8);

                const ribbonConfig = {
                    type: 7,
                    modelId: modelId,
                    state: 0,
                    color: ribbonColor,
                    customParams: {
                        maxPoints: this.config.maxTrailPoints,
                        wingSpan: 30.0,
                        minDistance: 10.0,
                        fadeAlpha: 0.1
                    }
                };

                this.effectManager.createEffect(ribbonConfig, modelId, 7);
                log(`✅ 已为模型 ${modelId} 添加飘带`, 'success');
            }

            setRibbonByDefault(enabled) {
                this.config.enableRibbonByDefault = enabled;
                log(`🎯 默认飘带设置: ${enabled ? '启用' : '禁用'}`, 'info');
            }

            addRibbonToAllModels() {
                const models = this.viewer.entities.values.filter(entity => 
                    entity.position && (entity.point || entity.model)
                );
                
                models.forEach(model => {
                    this.addRibbonEffect(model.id);
                });
                
                log(`➕ 已为 ${models.length} 个模型添加飘带`, 'success');
            }

            removeRibbonFromAllModels() {
                this.effectManager.destroyAllEffects();
                log(`➖ 已移除所有飘带效果`, 'info');
            }
        }

        // 初始化管理器
        const effectManager = new SimpleEffectManager(viewer);
        const modelManager = new SimpleModelManager(viewer, effectManager);

        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }

        // 调试函数
        function runDiagnostic() {
            log('🔍 开始运行诊断...', 'info');
            
            // 检查基础设置
            log(`📊 实体数量: ${viewer.entities.values.length}`, 'info');
            log(`📊 特效数量: ${effectManager.effects.size}`, 'info');
            log(`🎯 默认飘带: ${modelManager.config.enableRibbonByDefault ? '启用' : '禁用'}`, 'info');
            
            // 检查模型
            const modelsWithPosition = viewer.entities.values.filter(entity => entity.position);
            log(`📊 有位置的模型: ${modelsWithPosition.length}`, 'info');
            
            // 检查飘带效果
            const ribbonEffects = Array.from(effectManager.effects.values()).filter(effect => effect.type === 7);
            log(`📊 飘带效果: ${ribbonEffects.length}`, 'info');
            
            if (ribbonEffects.length === 0 && modelsWithPosition.length > 0) {
                log('⚠️ 有模型但没有飘带效果，尝试快速修复', 'warning');
            }
            
            log('✅ 诊断完成', 'success');
        }

        function createTestModel() {
            log('🧪 创建测试模型...', 'info');
            
            const testId = `test_model_${Date.now()}`;
            const position = Cesium.Cartesian3.fromDegrees(
                116.0 + Math.random() * 0.1, 
                39.0 + Math.random() * 0.1, 
                8000
            );
            
            modelManager.createTestModel(testId, position, {
                name: '测试飘带模型',
                color: Cesium.Color.fromRandom({ alpha: 1.0 })
            });
            
            // 创建简单的移动动画
            const startTime = Cesium.JulianDate.now();
            const stopTime = Cesium.JulianDate.addSeconds(startTime, 60, new Cesium.JulianDate());
            
            const entity = viewer.entities.getById(testId);
            if (entity) {
                // 创建移动路径
                const positions = [];
                const times = [];
                for (let i = 0; i <= 60; i++) {
                    times.push(Cesium.JulianDate.addSeconds(startTime, i, new Cesium.JulianDate()));
                    positions.push(Cesium.Cartesian3.fromDegrees(
                        116.0 + Math.sin(i * 0.1) * 0.01,
                        39.0 + Math.cos(i * 0.1) * 0.01,
                        8000 + Math.sin(i * 0.2) * 1000
                    ));
                }
                
                entity.position = new Cesium.SampledPositionProperty();
                entity.position.addSamples(times, positions);
                entity.orientation = new Cesium.VelocityOrientationProperty(entity.position);
                
                // 设置时钟
                viewer.clock.startTime = startTime;
                viewer.clock.stopTime = stopTime;
                viewer.clock.currentTime = startTime;
                viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP;
                viewer.clock.multiplier = 1;
                viewer.clock.shouldAnimate = true;
                
                log(`✅ 测试模型 ${testId} 已创建并开始移动`, 'success');
                
                // 定位相机
                viewer.camera.setView({
                    destination: Cesium.Cartesian3.fromDegrees(116.0, 39.0, 15000),
                    orientation: {
                        heading: 0.0,
                        pitch: -0.5,
                        roll: 0.0
                    }
                });
            }
        }

        function quickFix() {
            log('🔧 执行快速修复...', 'info');
            
            // 启用默认飘带
            modelManager.setRibbonByDefault(true);
            
            // 为所有模型添加飘带
            modelManager.addRibbonToAllModels();
            
            log('✅ 快速修复完成', 'success');
        }

        function enableRibbons() {
            modelManager.setRibbonByDefault(true);
        }

        function disableRibbons() {
            modelManager.setRibbonByDefault(false);
        }

        function addRibbonsToAll() {
            modelManager.addRibbonToAllModels();
        }

        function removeAllRibbons() {
            modelManager.removeRibbonFromAllModels();
        }

        function clearLog() {
            document.getElementById('logOutput').innerHTML = '<div class="log-info">📋 日志已清空</div>';
        }

        // 初始化
        log('🚀 飘带调试工具已加载', 'success');
        log('💡 点击"创建测试模型"开始测试', 'info');
    </script>
</body>
</html>
