<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单飘带测试</title>
    <script src="https://cesium.com/downloads/cesiumjs/releases/1.95/Build/Cesium/Cesium.js"></script>
    <link href="https://cesium.com/downloads/cesiumjs/releases/1.95/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <style>
        html, body, #cesiumContainer {
            width: 100%; height: 100%; margin: 0; padding: 0; overflow: hidden;
        }
        
        .control-panel {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: Arial, sans-serif;
            z-index: 1000;
        }
        
        .control-panel h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }
        
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }
        
        button:hover {
            background: #1976D2;
        }
        
        .status {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #F44336;
            color: #F44336;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
            color: #2196F3;
        }
    </style>
</head>
<body>
    <div id="cesiumContainer"></div>
    
    <div class="control-panel">
        <h3>🎗️ 简单飘带测试</h3>
        
        <div>
            <button onclick="createMovingAircraft()">✈️ 创建移动飞机</button>
            <button onclick="createCircularFlight()">🔄 创建圆形飞行</button>
            <button onclick="clearAll()">🗑️ 清除所有</button>
        </div>
        
        <div id="statusPanel">
            <div class="status info">点击按钮开始测试</div>
        </div>
    </div>

    <script>
        // 初始化 Cesium
        Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJhM2QyZGU5Ny1hM2RjLTQ0NjYtOWI5Ny1jY2RmNzIzMzE2ODAiLCJpZCI6MTM0ODg3LCJpYXQiOjE3NTEwMzM5Mjl9.frpOhwF27cIuEfRqTwS8_O0nhiQlCPG9klt0_AvNCZY';
        
        const viewer = new Cesium.Viewer('cesiumContainer', {
            timeline: true,
            animation: true,
            homeButton: false,
            sceneModePicker: false,
            baseLayerPicker: false,
            navigationHelpButton: false,
            fullscreenButton: false,
            vrButton: false
        });

        // 简单的飘带管理器
        class SimpleRibbonManager {
            constructor(viewer) {
                this.viewer = viewer;
                this.ribbons = new Map();
                this.config = {
                    maxPoints: 30,
                    baseWidth: 37.0, // 与aircraft-ribbon-demo保持一致
                    color: Cesium.Color.CYAN.withAlpha(0.8),
                    minDistance: 100.0 // 增加距离间隔
                };
            }

            addRibbon(aircraftId, color = null) {
                const aircraft = this.viewer.entities.getById(aircraftId);
                if (!aircraft) {
                    addStatus(`❌ 找不到飞机: ${aircraftId}`, 'error');
                    return;
                }

                const ribbonData = {
                    positions: [],
                    lastPosition: null,
                    color: color || this.config.color
                };

                // 创建飘带实体
                const ribbonEntity = this.viewer.entities.add({
                    id: `${aircraftId}_ribbon`,
                    polyline: {
                        positions: new Cesium.CallbackProperty(() => {
                            return this.updateRibbonPositions(aircraftId, ribbonData);
                        }, false),
                        width: this.config.baseWidth,
                        material: new Cesium.PolylineGlowMaterialProperty({
                            glowPower: 0.3,
                            color: ribbonData.color
                        }),
                        clampToGround: false
                    }
                });

                this.ribbons.set(aircraftId, {
                    entity: ribbonEntity,
                    data: ribbonData
                });

                addStatus(`✅ 为 ${aircraftId} 添加飘带`, 'success');
            }

            updateRibbonPositions(aircraftId, ribbonData) {
                const aircraft = this.viewer.entities.getById(aircraftId);
                if (!aircraft || !aircraft.position) {
                    return ribbonData.positions;
                }

                const currentTime = this.viewer.clock.currentTime;
                const currentPosition = aircraft.position.getValue(currentTime);
                
                if (!currentPosition) {
                    return ribbonData.positions;
                }

                // 检查是否需要添加新点
                if (!ribbonData.lastPosition || 
                    Cesium.Cartesian3.distance(currentPosition, ribbonData.lastPosition) > this.config.minDistance) {
                    
                    ribbonData.positions.push(currentPosition.clone());
                    ribbonData.lastPosition = currentPosition.clone();

                    // 限制点数
                    if (ribbonData.positions.length > this.config.maxPoints) {
                        ribbonData.positions.shift();
                    }

                    console.log(`飘带更新: ${aircraftId}, 点数: ${ribbonData.positions.length}`);
                }

                return ribbonData.positions;
            }

            removeRibbon(aircraftId) {
                const ribbon = this.ribbons.get(aircraftId);
                if (ribbon) {
                    this.viewer.entities.remove(ribbon.entity);
                    this.ribbons.delete(aircraftId);
                    addStatus(`🗑️ 移除 ${aircraftId} 的飘带`, 'info');
                }
            }

            clear() {
                this.ribbons.forEach((ribbon, aircraftId) => {
                    this.viewer.entities.remove(ribbon.entity);
                });
                this.ribbons.clear();
                addStatus('🗑️ 清除所有飘带', 'info');
            }
        }

        // 初始化飘带管理器
        const ribbonManager = new SimpleRibbonManager(viewer);

        // 状态显示函数
        function addStatus(message, type) {
            const statusPanel = document.getElementById('statusPanel');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusPanel.appendChild(statusDiv);
            
            // 保持最多5条状态信息
            while (statusPanel.children.length > 5) {
                statusPanel.removeChild(statusPanel.firstChild);
            }
            
            console.log(message);
        }

        // 创建移动飞机
        function createMovingAircraft() {
            const aircraftId = `aircraft_${Date.now()}`;
            
            // 创建直线飞行路径
            const startTime = Cesium.JulianDate.now();
            const stopTime = Cesium.JulianDate.addSeconds(startTime, 60, new Cesium.JulianDate());
            
            const property = new Cesium.SampledPositionProperty();
            
            // 直线飞行路径
            const waypoints = [
                { lon: 115.8, lat: 39.0, alt: 8000, time: 0 },
                { lon: 116.2, lat: 39.0, alt: 8000, time: 60 }
            ];
            
            waypoints.forEach(point => {
                const time = Cesium.JulianDate.addSeconds(startTime, point.time, new Cesium.JulianDate());
                const position = Cesium.Cartesian3.fromDegrees(point.lon, point.lat, point.alt);
                property.addSample(time, position);
            });
            
            // 创建飞机实体
            const aircraft = viewer.entities.add({
                id: aircraftId,
                position: property,
                orientation: new Cesium.VelocityOrientationProperty(property),
                point: {
                    pixelSize: 20,
                    color: Cesium.Color.YELLOW,
                    outlineColor: Cesium.Color.BLACK,
                    outlineWidth: 2
                },
                label: {
                    text: aircraftId,
                    font: '12px sans-serif',
                    fillColor: Cesium.Color.WHITE,
                    pixelOffset: new Cesium.Cartesian2(0, -30)
                }
            });
            
            // 设置时钟
            viewer.clock.startTime = startTime;
            viewer.clock.stopTime = stopTime;
            viewer.clock.currentTime = startTime;
            viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP;
            viewer.clock.multiplier = 5;
            viewer.clock.shouldAnimate = true;
            
            // 添加飘带
            ribbonManager.addRibbon(aircraftId, Cesium.Color.YELLOW.withAlpha(0.8));
            
            // 设置相机视角
            viewer.camera.setView({
                destination: Cesium.Cartesian3.fromDegrees(116.0, 39.0, 15000),
                orientation: {
                    heading: 0.0,
                    pitch: -0.5,
                    roll: 0.0
                }
            });
            
            addStatus(`✈️ 创建直线飞行: ${aircraftId}`, 'success');
        }

        // 创建圆形飞行
        function createCircularFlight() {
            const aircraftId = `circular_${Date.now()}`;
            
            const startTime = Cesium.JulianDate.now();
            const stopTime = Cesium.JulianDate.addSeconds(startTime, 120, new Cesium.JulianDate());
            
            const property = new Cesium.SampledPositionProperty();
            
            // 圆形飞行路径
            for (let i = 0; i <= 120; i++) {
                const time = Cesium.JulianDate.addSeconds(startTime, i, new Cesium.JulianDate());
                const angle = (i / 120) * Math.PI * 4; // 2圈
                const radius = 0.02; // 约2公里半径
                
                const lon = 116.0 + Math.cos(angle) * radius;
                const lat = 39.0 + Math.sin(angle) * radius;
                const alt = 8000 + Math.sin(angle * 2) * 1000; // 高度变化
                
                const position = Cesium.Cartesian3.fromDegrees(lon, lat, alt);
                property.addSample(time, position);
            }
            
            property.setInterpolationOptions({
                interpolationDegree: 2,
                interpolationAlgorithm: Cesium.HermitePolynomialApproximation
            });
            
            // 创建飞机实体
            const aircraft = viewer.entities.add({
                id: aircraftId,
                position: property,
                orientation: new Cesium.VelocityOrientationProperty(property),
                point: {
                    pixelSize: 20,
                    color: Cesium.Color.RED,
                    outlineColor: Cesium.Color.BLACK,
                    outlineWidth: 2
                },
                label: {
                    text: aircraftId,
                    font: '12px sans-serif',
                    fillColor: Cesium.Color.WHITE,
                    pixelOffset: new Cesium.Cartesian2(0, -30)
                }
            });
            
            // 设置时钟
            viewer.clock.startTime = startTime;
            viewer.clock.stopTime = stopTime;
            viewer.clock.currentTime = startTime;
            viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP;
            viewer.clock.multiplier = 3;
            viewer.clock.shouldAnimate = true;
            
            // 添加飘带
            ribbonManager.addRibbon(aircraftId, Cesium.Color.RED.withAlpha(0.8));
            
            addStatus(`🔄 创建圆形飞行: ${aircraftId}`, 'success');
        }

        // 清除所有
        function clearAll() {
            viewer.entities.removeAll();
            ribbonManager.clear();
            viewer.clock.shouldAnimate = false;
            addStatus('🗑️ 清除所有实体', 'info');
        }

        // 初始化提示
        addStatus('🎗️ 飘带测试已准备就绪', 'info');
    </script>
</body>
</html>
